import React, { useEffect, useState } from 'react';
import './About.css';

const About = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
    window.scrollTo(0, 0);
  }, []);

  const values = [
    {
      icon: '🌱',
      title: 'Sustainability',
      description: 'We practice eco-friendly farming methods that protect our environment for future generations.'
    },
    {
      icon: '💚',
      title: 'Quality',
      description: 'Every plant, flower, and produce is carefully cultivated to meet the highest standards of excellence.'
    },
    {
      icon: '🤝',
      title: 'Community',
      description: 'We believe in building strong relationships with our community and supporting local growth.'
    },
    {
      icon: '🌿',
      title: 'Natural',
      description: 'Our commitment to natural, organic practices ensures the purest and healthiest products.'
    }
  ];

  const features = [
    {
      title: 'Organic Farming',
      description: 'All our products are grown using organic methods without harmful pesticides or chemicals.',
      image: '/images/vegatebles in the garden.jpeg'
    },
    {
      title: 'Fresh Quality',
      description: 'We harvest our products at peak freshness to ensure maximum flavor and nutritional value.',
      image: '/images/flowers in garden in vase.jpeg'
    },
    {
      title: 'Sustainable Practices',
      description: 'Our farming methods are designed to protect the environment and promote biodiversity.',
      image: '/images/seedlings in the garden.jpeg'
    }
  ];

  return (
    <div className="about-page">
      {/* Hero Section */}
      <section className="about-hero">
        <div className="about-hero-background">
          <img 
            src="/images/mubwiza background image.png" 
            alt="Mubwiza Garden"
            className="about-hero-image"
          />
          <div className="about-hero-overlay"></div>
        </div>
        
        <div className="about-hero-content">
          <div className="container">
            <div className={`about-hero-text ${isVisible ? 'fade-in-up' : ''}`}>
              <h1 className="about-hero-title">About Mubwiza Garden</h1>
              <p className="about-hero-subtitle">
                Growing beauty, nourishing lives, and cultivating a sustainable future
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Story Section */}
      <section className="about-story section">
        <div className="container">
          <div className="story-content">
            <div className="story-text">
              <h2 className={`section-title ${isVisible ? 'fade-in-up' : ''}`}>Our Story</h2>
              <div className={`story-paragraphs ${isVisible ? 'fade-in-up' : ''}`}>
                <p>
                  Mubwiza Garden began as a dream to create a space where nature's beauty 
                  could flourish and be shared with our community. Founded with a passion 
                  for sustainable agriculture and a love for the natural world, our garden 
                  has grown into a thriving ecosystem that produces some of the finest 
                  flowers, vegetables, fruits, and herbs in the region.
                </p>
                <p>
                  Our journey started with a simple belief: that when we work in harmony 
                  with nature, we can create something truly beautiful and beneficial for 
                  everyone. Today, Mubwiza Garden stands as a testament to the power of 
                  sustainable farming, community support, and the endless beauty that 
                  nature provides.
                </p>
                <p>
                  Every plant in our garden is nurtured with care, grown using organic 
                  methods, and harvested at the perfect moment to ensure the highest 
                  quality. We take pride in offering products that not only look beautiful 
                  but also contribute to a healthier, more sustainable way of living.
                </p>
              </div>
            </div>
            <div className={`story-image ${isVisible ? 'fade-in-up' : ''}`}>
              <img 
                src="/images/flowers in garden in vase.jpeg" 
                alt="Garden story"
                className="story-img hover-scale"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="about-values section">
        <div className="container">
          <h2 className="section-title">Our Values</h2>
          <p className="section-subtitle">
            The principles that guide everything we do at Mubwiza Garden
          </p>
          
          <div className="values-grid">
            {values.map((value, index) => (
              <div 
                key={value.title}
                className={`value-card hover-lift ${isVisible ? 'fade-in-up' : ''}`}
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="value-icon">{value.icon}</div>
                <h3 className="value-title">{value.title}</h3>
                <p className="value-description">{value.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="about-features section">
        <div className="container">
          <h2 className="section-title">What Makes Us Special</h2>
          <div className="features-grid">
            {features.map((feature, index) => (
              <div 
                key={feature.title}
                className={`feature-card ${index % 2 === 0 ? 'feature-left' : 'feature-right'} ${isVisible ? 'fade-in-up' : ''}`}
                style={{ animationDelay: `${index * 0.2}s` }}
              >
                <div className="feature-image">
                  <img 
                    src={feature.image} 
                    alt={feature.title}
                    className="hover-scale"
                  />
                </div>
                <div className="feature-content">
                  <h3 className="feature-title">{feature.title}</h3>
                  <p className="feature-description">{feature.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="about-mission section">
        <div className="container">
          <div className="mission-content">
            <div className={`mission-text ${isVisible ? 'fade-in-up' : ''}`}>
              <h2 className="section-title">Our Mission</h2>
              <p className="mission-description">
                To cultivate and share nature's finest offerings while promoting 
                sustainable farming practices, supporting our local community, and 
                inspiring others to appreciate the beauty and bounty that comes from 
                working in harmony with the natural world.
              </p>
              <div className="mission-stats">
                <div className="stat">
                  <span className="stat-number">5+</span>
                  <span className="stat-label">Categories</span>
                </div>
                <div className="stat">
                  <span className="stat-number">100%</span>
                  <span className="stat-label">Organic</span>
                </div>
                <div className="stat">
                  <span className="stat-number">365</span>
                  <span className="stat-label">Days of Care</span>
                </div>
              </div>
            </div>
            <div className={`mission-images ${isVisible ? 'fade-in-up' : ''}`}>
              <img 
                src="/images/seedlings in the garden.jpeg" 
                alt="Mission"
                className="mission-img hover-scale"
              />
              <img 
                src="/images/tea spices.jpeg" 
                alt="Mission"
                className="mission-img hover-scale"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Contact CTA Section */}
      <section className="about-cta section">
        <div className="container">
          <div className={`cta-content ${isVisible ? 'fade-in-up' : ''}`}>
            <h2 className="cta-title">Visit Mubwiza Garden</h2>
            <p className="cta-description">
              Experience the beauty of our garden firsthand. We welcome visitors 
              to explore our growing spaces and learn about our sustainable practices.
            </p>
            <div className="cta-buttons">
              <a href="/contact" className="btn btn-primary">Get Directions</a>
              <a href="/" className="btn btn-secondary">Explore Our Products</a>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default About;
