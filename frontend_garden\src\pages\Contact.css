/* Contact Hero Section */
.contact-hero {
  position: relative;
  height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  margin-top: 80px;
}

.contact-hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

.contact-hero-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  animation: slowZoom 20s ease-in-out infinite alternate;
}

.contact-hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(45, 80, 22, 0.8),
    rgba(74, 124, 89, 0.7)
  );
}

.contact-hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  color: white;
  padding: 0 20px;
}

.contact-hero-title {
  font-family: 'Dancing Script', cursive;
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 20px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.contact-hero-subtitle {
  font-size: 1.3rem;
  font-weight: 300;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

/* Contact Info Section */
.contact-info {
  background: white;
}

.contact-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
}

.contact-card {
  background: white;
  padding: 40px 30px;
  border-radius: 20px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.contact-card:hover {
  border-color: var(--primary-green);
  transform: translateY(-10px);
}

.contact-icon {
  font-size: 3.5rem;
  margin-bottom: 20px;
  animation: bounce 2s infinite;
}

.contact-title {
  font-size: 1.4rem;
  font-weight: 600;
  color: var(--primary-green);
  margin-bottom: 15px;
}

.contact-details {
  margin-bottom: 15px;
}

.contact-detail {
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--text-dark);
  margin-bottom: 5px;
}

.contact-description {
  color: var(--text-light);
  line-height: 1.6;
  font-size: 0.95rem;
}

/* Map Section */
.contact-map {
  background: linear-gradient(135deg, var(--soft-white), var(--cream));
  position: relative;
}

.contact-map::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('/images/mubwiza background image.png') center/cover;
  opacity: 0.03;
  z-index: 0;
}

.map-container {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 40px;
  position: relative;
  z-index: 1;
}

.map-placeholder {
  background: linear-gradient(135deg, var(--primary-green), var(--secondary-green));
  border-radius: 15px;
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.map-placeholder::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('/images/mubwiza background image.png') center/cover;
  opacity: 0.1;
  z-index: 0;
}

.map-content {
  position: relative;
  z-index: 1;
}

.map-icon {
  font-size: 4rem;
  margin-bottom: 20px;
  animation: pulse 2s infinite;
}

.map-content h3 {
  font-size: 1.8rem;
  margin-bottom: 10px;
}

.map-content p {
  font-size: 1.1rem;
  margin-bottom: 10px;
}

.map-note {
  font-size: 0.95rem;
  opacity: 0.9;
  font-style: italic;
}

.map-info {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.map-info h3 {
  color: var(--primary-green);
  font-size: 1.5rem;
  margin-bottom: 20px;
}

.map-info p {
  color: var(--text-dark);
  line-height: 1.6;
  margin-bottom: 25px;
}

.direction-tips h4 {
  color: var(--primary-green);
  margin-bottom: 15px;
}

.direction-tips ul {
  list-style: none;
  padding: 0;
}

.direction-tips li {
  position: relative;
  padding: 8px 0 8px 25px;
  color: var(--text-dark);
}

.direction-tips li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--secondary-green);
  font-weight: bold;
}

/* Social Media Section */
.contact-social {
  background: white;
}

.social-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.social-card {
  background: white;
  padding: 30px;
  border-radius: 15px;
  text-align: center;
  text-decoration: none;
  color: inherit;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.social-card:hover {
  border-color: var(--accent-gold);
  transform: translateY(-5px);
  color: inherit;
}

.social-icon {
  font-size: 3rem;
  margin-bottom: 15px;
  animation: bounce 2s infinite;
}

.social-name {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--primary-green);
  margin-bottom: 10px;
}

.social-description {
  color: var(--text-light);
  line-height: 1.5;
  font-size: 0.95rem;
}

/* Visiting Tips Section */
.visiting-tips {
  background: linear-gradient(135deg, var(--cream), var(--soft-white));
  position: relative;
}

.visiting-tips::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('/images/mubwiza background image.png') center/cover;
  opacity: 0.03;
  z-index: 0;
}

.tips-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
  position: relative;
  z-index: 1;
}

.tip-card {
  background: white;
  padding: 30px;
  border-radius: 15px;
  text-align: center;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.tip-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.tip-icon {
  font-size: 3rem;
  margin-bottom: 20px;
  animation: pulse 2s infinite;
}

.tip-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--primary-green);
  margin-bottom: 15px;
}

.tip-description {
  color: var(--text-light);
  line-height: 1.6;
  font-size: 0.95rem;
}

/* CTA Section */
.contact-cta {
  background: linear-gradient(135deg, var(--primary-green), var(--secondary-green));
  color: white;
  text-align: center;
  position: relative;
}

.contact-cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('/images/mubwiza background image.png') center/cover;
  opacity: 0.1;
  z-index: 0;
}

.cta-content {
  position: relative;
  z-index: 1;
}

.cta-title {
  font-family: 'Dancing Script', cursive;
  font-size: 2.5rem;
  font-weight: 600;
  margin-bottom: 20px;
}

.cta-description {
  font-size: 1.2rem;
  line-height: 1.7;
  max-width: 600px;
  margin: 0 auto 40px;
  color: rgba(255, 255, 255, 0.95);
}

.cta-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .map-container {
    gap: 30px;
  }
}

@media (max-width: 768px) {
  .contact-hero {
    height: 50vh;
  }
  
  .contact-hero-title {
    font-size: 2.8rem;
  }
  
  .contact-grid {
    grid-template-columns: 1fr;
    gap: 25px;
  }
  
  .contact-card {
    padding: 30px 20px;
  }
  
  .map-container {
    grid-template-columns: 1fr;
    gap: 25px;
  }
  
  .map-placeholder {
    height: 300px;
  }
  
  .social-grid,
  .tips-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 25px;
  }
  
  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }
}

@media (max-width: 480px) {
  .contact-hero-title {
    font-size: 2.2rem;
  }
  
  .contact-hero-subtitle {
    font-size: 1.1rem;
  }
  
  .contact-icon,
  .social-icon,
  .tip-icon {
    font-size: 2.5rem;
  }
  
  .map-icon {
    font-size: 3rem;
  }
  
  .cta-title {
    font-size: 2rem;
  }
}
