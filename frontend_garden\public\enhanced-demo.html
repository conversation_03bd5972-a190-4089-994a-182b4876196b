<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mubwiza Garden - Nature's Beauty</title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="favicon.svg">
    <link rel="icon" type="image/x-icon" href="favicon.ico">
    <link rel="apple-touch-icon" href="apple-touch-icon.png">
    <meta name="theme-color" content="#2d5016">

    <!-- Fonts and Styles -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Dancing+Script:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'garden': {
                            'primary': '#2d5016',
                            'secondary': '#4a7c59',
                            'light': '#8fbc8f',
                            'accent': '#daa520',
                            'cream': '#f5f5dc',
                            'soft': '#fafafa',
                        }
                    },
                    fontFamily: {
                        'dancing': ['Dancing Script', 'cursive'],
                        'poppins': ['Poppins', 'sans-serif'],
                    },
                    animation: {
                        'bounce-slow': 'bounce 2s infinite',
                        'zoom': 'zoom 20s ease-in-out infinite alternate',
                        'fade-in-up': 'fadeInUp 0.8s ease-out',
                        'float': 'float 6s ease-in-out infinite',
                        'glow': 'glow 2s ease-in-out infinite alternate',
                        'slide-in-left': 'slideInLeft 0.8s ease-out',
                        'slide-in-right': 'slideInRight 0.8s ease-out',
                    },
                    keyframes: {
                        zoom: {
                            '0%': { transform: 'scale(1)' },
                            '100%': { transform: 'scale(1.1)' },
                        },
                        fadeInUp: {
                            '0%': { opacity: '0', transform: 'translateY(30px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' },
                        },
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-20px)' },
                        },
                        glow: {
                            '0%': { boxShadow: '0 0 20px rgba(218, 165, 32, 0.5)' },
                            '100%': { boxShadow: '0 0 30px rgba(218, 165, 32, 0.8)' },
                        },
                        slideInLeft: {
                            '0%': { opacity: '0', transform: 'translateX(-50px)' },
                            '100%': { opacity: '1', transform: 'translateX(0)' },
                        },
                        slideInRight: {
                            '0%': { opacity: '0', transform: 'translateX(50px)' },
                            '100%': { opacity: '1', transform: 'translateX(0)' },
                        },
                    }
                }
            }
        }
    </script>
    <style>
        .hero-bg {
            background: linear-gradient(135deg, rgba(45, 80, 22, 0.7), rgba(74, 124, 89, 0.6), rgba(45, 80, 22, 0.8));
        }
        .garden-gradient {
            background: linear-gradient(135deg, #2d5016, #4a7c59);
        }
        .accent-gradient {
            background: linear-gradient(135deg, #daa520, #ffd700);
        }
        .text-gradient {
            background: linear-gradient(135deg, #daa520, #ffd700);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .card-hover {
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }
        .card-hover:hover {
            transform: translateY(-15px) scale(1.02);
        }
        .parallax {
            transform: translateZ(0);
            will-change: transform;
        }
        .floating-elements {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            pointer-events: none;
        }
        .floating-element {
            position: absolute;
            opacity: 0.1;
            animation: float 8s ease-in-out infinite;
        }
        .floating-element:nth-child(1) { left: 10%; animation-delay: 0s; }
        .floating-element:nth-child(2) { left: 20%; animation-delay: 1s; }
        .floating-element:nth-child(3) { left: 30%; animation-delay: 2s; }
        .floating-element:nth-child(4) { left: 40%; animation-delay: 3s; }
        .floating-element:nth-child(5) { left: 50%; animation-delay: 4s; }
        .floating-element:nth-child(6) { left: 60%; animation-delay: 5s; }
        .floating-element:nth-child(7) { left: 70%; animation-delay: 6s; }
        .floating-element:nth-child(8) { left: 80%; animation-delay: 7s; }
    </style>
</head>
<body class="font-poppins overflow-x-hidden bg-gray-50">
    <!-- Floating Background Elements -->
    <div class="floating-elements fixed inset-0 z-0">
        <div class="floating-element text-6xl text-garden-light/20"><i class="fas fa-seedling"></i></div>
        <div class="floating-element text-5xl text-garden-secondary/20"><i class="fas fa-leaf"></i></div>
        <div class="floating-element text-4xl text-garden-accent/20"><i class="fas fa-spa"></i></div>
        <div class="floating-element text-6xl text-garden-light/20"><i class="fas fa-flower"></i></div>
        <div class="floating-element text-5xl text-garden-secondary/20"><i class="fas fa-tree"></i></div>
        <div class="floating-element text-4xl text-garden-accent/20"><i class="fas fa-pagelines"></i></div>
        <div class="floating-element text-6xl text-garden-light/20"><i class="fas fa-envira"></i></div>
        <div class="floating-element text-5xl text-garden-secondary/20"><i class="fas fa-cannabis"></i></div>
    </div>

    <!-- Header -->
    <header class="fixed top-0 left-0 right-0 z-50 glass-effect transition-all duration-300">
        <div class="max-w-7xl mx-auto px-6">
            <div class="flex items-center justify-between py-4">
                <a href="#" class="flex items-center font-dancing text-3xl font-semibold text-garden-primary hover:scale-105 transition-transform duration-300 animate-glow">
                    <div class="w-12 h-12 bg-gradient-to-br from-garden-primary to-garden-secondary rounded-full flex items-center justify-center mr-3 animate-bounce-slow shadow-lg">
                        <i class="fas fa-seedling text-white text-xl"></i>
                    </div>
                    <span class="text-gradient">Mubwiza Garden</span>
                </a>
                <nav class="hidden md:flex space-x-8">
                    <a href="#" class="text-gray-700 hover:text-garden-primary font-medium transition-all duration-300 relative group">
                        Home
                        <span class="absolute bottom-0 left-0 w-0 h-0.5 accent-gradient transition-all duration-300 group-hover:w-full"></span>
                    </a>
                    <a href="#categories" class="text-gray-700 hover:text-garden-primary font-medium transition-all duration-300 relative group">
                        Categories
                        <span class="absolute bottom-0 left-0 w-0 h-0.5 accent-gradient transition-all duration-300 group-hover:w-full"></span>
                    </a>
                    <a href="#about" class="text-gray-700 hover:text-garden-primary font-medium transition-all duration-300 relative group">
                        About
                        <span class="absolute bottom-0 left-0 w-0 h-0.5 accent-gradient transition-all duration-300 group-hover:w-full"></span>
                    </a>
                    <a href="#contact" class="text-gray-700 hover:text-garden-primary font-medium transition-all duration-300 relative group">
                        Contact
                        <span class="absolute bottom-0 left-0 w-0 h-0.5 accent-gradient transition-all duration-300 group-hover:w-full"></span>
                    </a>
                </nav>
                <button class="md:hidden text-garden-primary hover:scale-110 transition-transform duration-300">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="relative h-screen flex items-center justify-center overflow-hidden">
        <div class="absolute inset-0 z-0">
            <img src="images/mubwiza background image.png" alt="Mubwiza Garden" class="w-full h-full object-cover animate-zoom parallax">
            <div class="absolute inset-0 hero-bg"></div>
        </div>

        <div class="relative z-10 text-center text-white px-6 max-w-6xl mx-auto">
            <div class="animate-fade-in-up">
                <h1 class="font-dancing text-6xl md:text-8xl lg:text-9xl font-bold mb-8 drop-shadow-2xl leading-tight">
                    Welcome to <span class="text-gradient animate-glow">Mubwiza Garden</span>
                </h1>
                <p class="text-xl md:text-3xl font-light mb-12 leading-relaxed drop-shadow-lg max-w-5xl mx-auto animate-slide-in-left" style="animation-delay: 0.3s;">
                    Where nature's beauty comes alive through our carefully cultivated
                    flowers, fresh vegetables, seasonal fruits, aromatic spices, and quality seedlings.
                </p>
                <div class="flex flex-col sm:flex-row gap-6 justify-center animate-slide-in-right" style="animation-delay: 0.6s;">
                    <a href="#categories" class="garden-gradient text-white px-10 py-5 rounded-full font-semibold text-lg hover:-translate-y-2 hover:shadow-2xl transition-all duration-500 transform hover:scale-105 animate-glow flex items-center">
                        <i class="fas fa-leaf mr-3"></i>
                        Explore Our Garden
                    </a>
                    <a href="#about" class="glass-effect text-white px-10 py-5 rounded-full font-semibold text-lg hover:bg-white hover:text-garden-primary transition-all duration-500 transform hover:scale-105 flex items-center">
                        <i class="fas fa-info-circle mr-3"></i>
                        Learn More
                    </a>
                </div>
            </div>
        </div>

        <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce z-10">
            <div class="flex flex-col items-center space-y-2">
                <span class="text-sm font-medium">Scroll to explore</span>
                <svg class="w-8 h-8 animate-bounce" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                </svg>
            </div>
        </div>
    </section>

    <!-- Categories Section -->
    <section id="categories" class="py-24 bg-gradient-to-br from-garden-soft to-garden-cream relative overflow-hidden">
        <div class="absolute inset-0 opacity-5">
            <img src="images/mubwiza background image.png" alt="" class="w-full h-full object-cover parallax">
        </div>

        <div class="max-w-7xl mx-auto px-6 relative z-10">
            <div class="text-center mb-20">
                <h2 class="font-dancing text-6xl md:text-7xl font-semibold text-garden-primary mb-8 relative animate-fade-in-up">
                    Our Garden Categories
                    <span class="absolute -bottom-4 left-1/2 transform -translate-x-1/2 w-24 h-1 accent-gradient rounded animate-glow"></span>
                </h2>
                <p class="text-2xl text-gray-600 max-w-3xl mx-auto leading-relaxed animate-slide-in-left">
                    Discover the diverse beauty and bounty of Mubwiza Garden
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-8">
                <!-- Flowers -->
                <div class="group card-hover bg-white rounded-3xl overflow-hidden shadow-xl hover:shadow-2xl cursor-pointer transform perspective-1000">
                    <div class="relative h-72 overflow-hidden">
                        <img src="images/flowers categories.jpg" alt="Flowers" class="w-full h-full object-cover group-hover:scale-125 transition-transform duration-700">
                        <div class="absolute inset-0 bg-gradient-to-t from-garden-primary/90 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500 flex items-center justify-center">
                            <div class="text-center transform translate-y-8 group-hover:translate-y-0 transition-transform duration-500">
                                <div class="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4 animate-bounce-slow">
                                    <i class="fas fa-flower text-white text-4xl"></i>
                                </div>
                                <p class="text-white font-semibold text-lg">Explore Flowers</p>
                            </div>
                        </div>
                        <div class="absolute top-4 right-4 glass-effect text-white px-3 py-1 rounded-full text-sm font-medium">
                            Premium
                        </div>
                    </div>
                    <div class="p-8">
                        <h3 class="text-2xl font-bold text-garden-primary mb-4 flex items-center">
                            <div class="w-10 h-10 bg-gradient-to-br from-pink-400 to-rose-500 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-flower text-white"></i>
                            </div>
                            Flowers
                        </h3>
                        <p class="text-gray-600 leading-relaxed mb-6">Beautiful fresh flowers for all occasions - decorations, gifts, and special events</p>
                        <div class="flex items-center text-garden-secondary font-semibold opacity-0 group-hover:opacity-100 transition-all duration-500 transform translate-x-4 group-hover:translate-x-0">
                            <span>Explore Collection</span>
                            <svg class="w-5 h-5 ml-2 transform group-hover:translate-x-2 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- Vegetables -->
                <div class="group card-hover bg-white rounded-3xl overflow-hidden shadow-xl hover:shadow-2xl cursor-pointer transform perspective-1000">
                    <div class="relative h-72 overflow-hidden">
                        <img src="images/vegetables categories.jpg" alt="Vegetables" class="w-full h-full object-cover group-hover:scale-125 transition-transform duration-700">
                        <div class="absolute inset-0 bg-gradient-to-t from-garden-primary/90 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500 flex items-center justify-center">
                            <div class="text-center transform translate-y-8 group-hover:translate-y-0 transition-transform duration-500">
                                <div class="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4 animate-bounce-slow">
                                    <i class="fas fa-carrot text-white text-4xl"></i>
                                </div>
                                <p class="text-white font-semibold text-lg">Explore Vegetables</p>
                            </div>
                        </div>
                        <div class="absolute top-4 right-4 glass-effect text-white px-3 py-1 rounded-full text-sm font-medium">
                            Organic
                        </div>
                    </div>
                    <div class="p-8">
                        <h3 class="text-2xl font-bold text-garden-primary mb-4 flex items-center">
                            <div class="w-10 h-10 bg-gradient-to-br from-green-400 to-emerald-500 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-carrot text-white"></i>
                            </div>
                            Vegetables
                        </h3>
                        <p class="text-gray-600 leading-relaxed mb-6">Fresh, organic vegetables grown with sustainable farming practices</p>
                        <div class="flex items-center text-garden-secondary font-semibold opacity-0 group-hover:opacity-100 transition-all duration-500 transform translate-x-4 group-hover:translate-x-0">
                            <span>Explore Collection</span>
                            <svg class="w-5 h-5 ml-2 transform group-hover:translate-x-2 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- Fruits -->
                <div class="group card-hover bg-white rounded-3xl overflow-hidden shadow-xl hover:shadow-2xl cursor-pointer transform perspective-1000">
                    <div class="relative h-72 overflow-hidden">
                        <img src="images/fruits categories.jpg" alt="Fruits" class="w-full h-full object-cover group-hover:scale-125 transition-transform duration-700">
                        <div class="absolute inset-0 bg-gradient-to-t from-garden-primary/90 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500 flex items-center justify-center">
                            <div class="text-center transform translate-y-8 group-hover:translate-y-0 transition-transform duration-500">
                                <div class="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4 animate-bounce-slow">
                                    <i class="fas fa-apple-alt text-white text-4xl"></i>
                                </div>
                                <p class="text-white font-semibold text-lg">Explore Fruits</p>
                            </div>
                        </div>
                        <div class="absolute top-4 right-4 glass-effect text-white px-3 py-1 rounded-full text-sm font-medium">
                            Seasonal
                        </div>
                    </div>
                    <div class="p-8">
                        <h3 class="text-2xl font-bold text-garden-primary mb-4 flex items-center">
                            <div class="w-10 h-10 bg-gradient-to-br from-red-400 to-pink-500 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-apple-alt text-white"></i>
                            </div>
                            Fruits
                        </h3>
                        <p class="text-gray-600 leading-relaxed mb-6">Sweet, seasonal fruits harvested at peak ripeness for maximum flavor</p>
                        <div class="flex items-center text-garden-secondary font-semibold opacity-0 group-hover:opacity-100 transition-all duration-500 transform translate-x-4 group-hover:translate-x-0">
                            <span>Explore Collection</span>
                            <svg class="w-5 h-5 ml-2 transform group-hover:translate-x-2 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- Tea & Spices -->
                <div class="group card-hover bg-white rounded-3xl overflow-hidden shadow-xl hover:shadow-2xl cursor-pointer transform perspective-1000">
                    <div class="relative h-72 overflow-hidden">
                        <img src="images/tea spices categories.jpg" alt="Tea & Spices" class="w-full h-full object-cover group-hover:scale-125 transition-transform duration-700">
                        <div class="absolute inset-0 bg-gradient-to-t from-garden-primary/90 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500 flex items-center justify-center">
                            <div class="text-center transform translate-y-8 group-hover:translate-y-0 transition-transform duration-500">
                                <div class="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4 animate-bounce-slow">
                                    <i class="fas fa-leaf text-white text-4xl"></i>
                                </div>
                                <p class="text-white font-semibold text-lg">Explore Tea & Spices</p>
                            </div>
                        </div>
                        <div class="absolute top-4 right-4 glass-effect text-white px-3 py-1 rounded-full text-sm font-medium">
                            Aromatic
                        </div>
                    </div>
                    <div class="p-8">
                        <h3 class="text-2xl font-bold text-garden-primary mb-4 flex items-center">
                            <div class="w-10 h-10 bg-gradient-to-br from-emerald-400 to-teal-500 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-leaf text-white"></i>
                            </div>
                            Tea & Spices
                        </h3>
                        <p class="text-gray-600 leading-relaxed mb-6">Aromatic herbs and spices to enhance your culinary experience</p>
                        <div class="flex items-center text-garden-secondary font-semibold opacity-0 group-hover:opacity-100 transition-all duration-500 transform translate-x-4 group-hover:translate-x-0">
                            <span>Explore Collection</span>
                            <svg class="w-5 h-5 ml-2 transform group-hover:translate-x-2 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- Seedlings -->
                <div class="group card-hover bg-white rounded-3xl overflow-hidden shadow-xl hover:shadow-2xl cursor-pointer transform perspective-1000">
                    <div class="relative h-72 overflow-hidden">
                        <img src="images/seedling categories.jpg" alt="Seedlings" class="w-full h-full object-cover group-hover:scale-125 transition-transform duration-700">
                        <div class="absolute inset-0 bg-gradient-to-t from-garden-primary/90 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500 flex items-center justify-center">
                            <div class="text-center transform translate-y-8 group-hover:translate-y-0 transition-transform duration-500">
                                <div class="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4 animate-bounce-slow">
                                    <i class="fas fa-seedling text-white text-4xl"></i>
                                </div>
                                <p class="text-white font-semibold text-lg">Explore Seedlings</p>
                            </div>
                        </div>
                        <div class="absolute top-4 right-4 glass-effect text-white px-3 py-1 rounded-full text-sm font-medium">
                            Growing
                        </div>
                    </div>
                    <div class="p-8">
                        <h3 class="text-2xl font-bold text-garden-primary mb-4 flex items-center">
                            <div class="w-10 h-10 bg-gradient-to-br from-lime-400 to-green-500 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-seedling text-white"></i>
                            </div>
                            Seedlings
                        </h3>
                        <p class="text-gray-600 leading-relaxed mb-6">Quality seedlings to help you start your own beautiful garden</p>
                        <div class="flex items-center text-garden-secondary font-semibold opacity-0 group-hover:opacity-100 transition-all duration-500 transform translate-x-4 group-hover:translate-x-0">
                            <span>Explore Collection</span>
                            <svg class="w-5 h-5 ml-2 transform group-hover:translate-x-2 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-24 bg-white relative overflow-hidden">
        <div class="max-w-7xl mx-auto px-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
                <div class="space-y-8 animate-slide-in-left">
                    <h2 class="font-dancing text-6xl font-semibold text-garden-primary relative">
                        Growing Beauty, Naturally
                        <span class="absolute -bottom-3 left-0 w-24 h-1 accent-gradient rounded animate-glow"></span>
                    </h2>
                    <p class="text-xl text-gray-700 leading-relaxed">
                        At Mubwiza Garden, we believe in the power of nature to bring joy,
                        nourishment, and beauty to life. Our garden is a testament to sustainable
                        farming practices and the careful cultivation of nature's finest offerings.
                    </p>
                    <p class="text-lg text-gray-600 leading-relaxed">
                        From vibrant flowers that brighten any space to fresh vegetables that
                        nourish the body, every plant in our garden is grown with love, care,
                        and respect for the environment.
                    </p>
                    <div class="flex flex-wrap gap-6">
                        <div class="flex items-center space-x-3">
                            <div class="w-12 h-12 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-seedling text-white"></i>
                            </div>
                            <span class="font-semibold text-garden-primary">100% Organic</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-12 h-12 bg-gradient-to-br from-emerald-400 to-teal-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-recycle text-white"></i>
                            </div>
                            <span class="font-semibold text-garden-primary">Sustainable</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-12 h-12 bg-gradient-to-br from-teal-400 to-cyan-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-heart text-white"></i>
                            </div>
                            <span class="font-semibold text-garden-primary">Eco-Friendly</span>
                        </div>
                    </div>
                    <a href="#contact" class="inline-block garden-gradient text-white px-8 py-4 rounded-full font-semibold hover:-translate-y-1 hover:shadow-xl transition-all duration-300 animate-glow">
                        Visit Our Garden
                    </a>
                </div>
                <div class="grid grid-cols-2 gap-6 animate-slide-in-right">
                    <img
                        src="images/flowers in garden in vase.jpeg"
                        alt="Garden flowers"
                        class="w-full h-80 object-cover rounded-3xl shadow-xl hover:scale-105 transition-transform duration-500 transform -translate-y-8"
                    />
                    <img
                        src="images/seedlings in the garden.jpeg"
                        alt="Garden seedlings"
                        class="w-full h-80 object-cover rounded-3xl shadow-xl hover:scale-105 transition-transform duration-500 transform translate-y-8"
                    />
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer id="contact" class="garden-gradient text-white relative overflow-hidden">
        <div class="absolute inset-0 opacity-10">
            <img src="images/mubwiza background image.png" alt="" class="w-full h-full object-cover parallax">
        </div>

        <div class="max-w-7xl mx-auto px-6 py-20 relative z-10">
            <div class="text-center mb-16">
                <div class="font-dancing text-5xl font-semibold mb-8 flex items-center justify-center animate-glow">
                    <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mr-4 animate-bounce-slow">
                        <i class="fas fa-seedling text-white text-2xl"></i>
                    </div>
                    Mubwiza Garden
                </div>
                <p class="text-xl mb-12 max-w-3xl mx-auto leading-relaxed opacity-90">
                    Welcome to Mubwiza Garden, where nature's beauty comes alive.
                    We showcase the finest flowers, fresh vegetables, seasonal fruits,
                    aromatic tea & spices, and quality seedlings.
                </p>

                <div class="flex justify-center space-x-6 mb-12">
                    <a href="#" class="w-16 h-16 glass-effect rounded-full flex items-center justify-center hover:bg-blue-500 hover:-translate-y-2 hover:scale-110 transition-all duration-300 group">
                        <i class="fab fa-facebook-f text-white text-xl group-hover:animate-bounce"></i>
                    </a>
                    <a href="#" class="w-16 h-16 glass-effect rounded-full flex items-center justify-center hover:bg-pink-500 hover:-translate-y-2 hover:scale-110 transition-all duration-300 group">
                        <i class="fab fa-instagram text-white text-xl group-hover:animate-bounce"></i>
                    </a>
                    <a href="#" class="w-16 h-16 glass-effect rounded-full flex items-center justify-center hover:bg-green-500 hover:-translate-y-2 hover:scale-110 transition-all duration-300 group">
                        <i class="fab fa-whatsapp text-white text-xl group-hover:animate-bounce"></i>
                    </a>
                    <a href="#" class="w-16 h-16 glass-effect rounded-full flex items-center justify-center hover:bg-blue-400 hover:-translate-y-2 hover:scale-110 transition-all duration-300 group">
                        <i class="fab fa-twitter text-white text-xl group-hover:animate-bounce"></i>
                    </a>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-map-marker-alt text-white text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold mb-2 text-garden-accent">Location</h3>
                        <p class="opacity-90 leading-relaxed">
                            Mubwiza Garden<br>
                            Muhabura Integrated Polytechnic College (MIPC)<br>
                            Northern Province, Musanze, Muhoza<br>
                            Rwanda
                        </p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-phone text-white text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold mb-2 text-garden-accent">Phone</h3>
                        <p class="opacity-90">+250 XXX XXX XXX</p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-clock text-white text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold mb-2 text-garden-accent">Hours</h3>
                        <p class="opacity-90">Mon - Sat: 8:00 AM - 6:00 PM</p>
                    </div>
                </div>

                <div class="border-t border-white/20 pt-8">
                    <div class="h-px bg-gradient-to-r from-transparent via-garden-accent to-transparent mb-6 animate-glow"></div>
                    <p class="text-white/80 mb-3">&copy; 2025 Mubwiza Garden. All rights reserved.</p>
                    <p class="text-garden-accent font-medium text-xl italic animate-pulse flex items-center justify-center">
                        <i class="fas fa-leaf mr-2"></i>
                        Growing beauty, naturally
                        <i class="fas fa-leaf ml-2"></i>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Enhanced smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Parallax effect
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const parallaxElements = document.querySelectorAll('.parallax');

            parallaxElements.forEach(element => {
                const speed = 0.5;
                element.style.transform = `translateY(${scrolled * speed}px)`;
            });
        });

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0) scale(1)';
                }
            });
        }, observerOptions);

        // Observe elements for animation
        document.querySelectorAll('.card-hover, .animate-fade-in-up, .animate-slide-in-left, .animate-slide-in-right').forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(30px) scale(0.95)';
            el.style.transition = 'opacity 0.8s ease, transform 0.8s ease';
            observer.observe(el);
        });

        // Add floating animation to background elements
        const floatingElements = document.querySelectorAll('.floating-element');
        floatingElements.forEach((element, index) => {
            element.style.animationDelay = `${index * 0.5}s`;
            element.style.top = `${Math.random() * 100}%`;
        });

        // Dynamic header background on scroll
        window.addEventListener('scroll', () => {
            const header = document.querySelector('header');
            if (window.scrollY > 100) {
                header.style.background = 'rgba(255, 255, 255, 0.95)';
                header.style.backdropFilter = 'blur(20px)';
                header.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.1)';
            } else {
                header.style.background = 'rgba(255, 255, 255, 0.1)';
                header.style.backdropFilter = 'blur(10px)';
                header.style.boxShadow = 'none';
            }
        });
    </script>
</body>
</html>