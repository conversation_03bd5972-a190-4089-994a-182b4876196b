import React, { useEffect, useState } from 'react';
import { usePara<PERSON>, Link } from 'react-router-dom';
import './CategoryPage.css';

const CategoryPage = () => {
  const { categoryName } = useParams();
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
    window.scrollTo(0, 0);
  }, [categoryName]);

  // Static data for each category
  const categoryData = {
    flowers: {
      title: 'Beautiful Flowers',
      description: 'Fresh, vibrant flowers perfect for decoration, gifts, and special occasions',
      icon: '🌹',
      heroImage: '/images/flowers in garden in vase.jpeg',
      categoryImage: '/images/flowers categories.jpg',
      products: [
        {
          name: 'Red Roses',
          description: 'Beautiful red roses perfect for expressing love and appreciation. Hand-picked from our garden at peak bloom.',
          image: '/images/red roses.jpeg',
          features: ['Fresh cut', 'Long-lasting', 'Perfect for gifts', 'Romantic occasions']
        }
      ],
      gallery: [
        '/images/red roses.jpeg',
        '/images/flowers in garden in vase.jpeg',
        '/images/flowers categories.jpg'
      ]
    },
    vegetables: {
      title: 'Fresh Vegetables',
      description: 'Organic vegetables grown with sustainable farming practices, rich in nutrients and flavor',
      icon: '🥕',
      heroImage: '/images/vegatebles in the garden.jpeg',
      categoryImage: '/images/vegetables categories.jpg',
      products: [
        {
          name: 'Fresh Tomatoes',
          description: 'Juicy organic tomatoes grown in our fertile gardens. Rich in vitamins and perfect for cooking.',
          image: '/images/tomatoes.jpeg',
          features: ['Organic', 'Vine-ripened', 'Rich in vitamins', 'Pesticide-free']
        }
      ],
      gallery: [
        '/images/tomatoes.jpeg',
        '/images/vegatebles in the garden.jpeg',
        '/images/vegetables categories.jpg'
      ]
    },
    fruits: {
      title: 'Seasonal Fruits',
      description: 'Sweet, fresh fruits harvested at peak ripeness for maximum flavor and nutrition',
      icon: '🍓',
      heroImage: '/images/strowberries.jpeg',
      categoryImage: '/images/fruits categories.jpg',
      products: [
        {
          name: 'Sweet Strawberries',
          description: 'Sweet, succulent strawberries bursting with natural flavor. Rich in vitamin C and antioxidants.',
          image: '/images/strowberries.jpeg',
          features: ['High in Vitamin C', 'Natural antioxidants', 'Sweet flavor', 'Fresh picked']
        }
      ],
      gallery: [
        '/images/strowberries.jpeg',
        '/images/fruits categories.jpg'
      ]
    },
    'tea-spices': {
      title: 'Tea & Spices',
      description: 'Aromatic herbs and spices to enhance your culinary experience and wellness',
      icon: '🌿',
      heroImage: '/images/tea spices.jpeg',
      categoryImage: '/images/tea spices categories.jpg',
      products: [
        {
          name: 'Fresh Mint Tea',
          description: 'Aromatic fresh mint leaves perfect for brewing refreshing tea. Grown organically in our herb garden.',
          image: '/images/mint tea.jpeg',
          features: ['Organic herbs', 'Digestive benefits', 'Refreshing taste', 'Natural oils']
        }
      ],
      gallery: [
        '/images/mint tea.jpeg',
        '/images/tea spices.jpeg',
        '/images/tea spices categories.jpg'
      ]
    },
    seedlings: {
      title: 'Quality Seedlings',
      description: 'Healthy seedlings and young plants to help you start your own garden',
      icon: '🌱',
      heroImage: '/images/seedlings in the garden.jpeg',
      categoryImage: '/images/seedling categories.jpg',
      products: [
        {
          name: 'Tomato Seedlings',
          description: 'Healthy, robust tomato seedlings ready for transplanting. Strong root systems and disease-resistant varieties.',
          image: '/images/tomato seedling.jpeg',
          features: ['Disease resistant', 'Strong roots', 'Ready to plant', 'Growing guide included']
        }
      ],
      gallery: [
        '/images/tomato seedling.jpeg',
        '/images/seedlings in the garden.jpeg',
        '/images/seedling categories.jpg'
      ]
    }
  };

  const category = categoryData[categoryName];

  if (!category) {
    return (
      <div className="category-not-found">
        <div className="container">
          <h1>Category Not Found</h1>
          <p>The category you're looking for doesn't exist.</p>
          <Link to="/" className="btn btn-primary">Return Home</Link>
        </div>
      </div>
    );
  }

  return (
    <div className="category-page">
      {/* Hero Section */}
      <section className="category-hero">
        <div className="category-hero-background">
          <img 
            src={category.heroImage} 
            alt={category.title}
            className="category-hero-image"
          />
          <div className="category-hero-overlay"></div>
        </div>
        
        <div className="category-hero-content">
          <div className="container">
            <div className={`category-hero-text ${isVisible ? 'fade-in-up' : ''}`}>
              <div className="category-breadcrumb">
                <Link to="/">Home</Link>
                <span>→</span>
                <span>{category.title}</span>
              </div>
              <div className="category-icon-large">{category.icon}</div>
              <h1 className="category-hero-title">{category.title}</h1>
              <p className="category-hero-description">{category.description}</p>
            </div>
          </div>
        </div>
      </section>

      {/* Products Section */}
      <section className="category-products section">
        <div className="container">
          <h2 className="section-title">Featured Products</h2>
          <div className="products-grid">
            {category.products.map((product, index) => (
              <div 
                key={product.name}
                className={`product-card card hover-lift ${isVisible ? 'fade-in-up' : ''}`}
                style={{ animationDelay: `${index * 0.2}s` }}
              >
                <div className="product-image-container">
                  <img 
                    src={product.image} 
                    alt={product.name}
                    className="product-image"
                  />
                </div>
                <div className="product-content">
                  <h3 className="product-title">{product.name}</h3>
                  <p className="product-description">{product.description}</p>
                  <div className="product-features">
                    <h4>Key Features:</h4>
                    <ul>
                      {product.features.map((feature, idx) => (
                        <li key={idx}>{feature}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Gallery Section */}
      <section className="category-gallery section">
        <div className="container">
          <h2 className="section-title">Gallery</h2>
          <p className="section-subtitle">
            Explore the beauty of our {category.title.toLowerCase()}
          </p>
          <div className="gallery-grid">
            {category.gallery.map((image, index) => (
              <div 
                key={index}
                className={`gallery-item hover-scale ${isVisible ? 'fade-in-up' : ''}`}
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <img 
                  src={image} 
                  alt={`${category.title} ${index + 1}`}
                  className="gallery-image"
                />
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Other Categories Section */}
      <section className="other-categories section">
        <div className="container">
          <h2 className="section-title">Explore Other Categories</h2>
          <div className="other-categories-grid">
            {Object.entries(categoryData)
              .filter(([key]) => key !== categoryName)
              .map(([key, cat]) => (
                <Link 
                  key={key}
                  to={`/category/${key}`}
                  className="other-category-card hover-lift"
                >
                  <img 
                    src={cat.categoryImage} 
                    alt={cat.title}
                    className="other-category-image"
                  />
                  <div className="other-category-content">
                    <span className="other-category-icon">{cat.icon}</span>
                    <h3 className="other-category-title">{cat.title}</h3>
                  </div>
                </Link>
              ))}
          </div>
        </div>
      </section>
    </div>
  );
};

export default CategoryPage;
