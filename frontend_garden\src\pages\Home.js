import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';

const Home = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const categories = [
    {
      name: 'Flowers',
      description: 'Beautiful fresh flowers for all occasions',
      image: '/images/flowers categories.jpg',
      path: '/category/flowers',
      icon: '🌹'
    },
    {
      name: 'Vegetables',
      description: 'Fresh, organic vegetables grown with care',
      image: '/images/vegetables categories.jpg',
      path: '/category/vegetables',
      icon: '🥕'
    },
    {
      name: 'Fruits',
      description: 'Sweet, seasonal fruits at peak ripeness',
      image: '/images/fruits categories.jpg',
      path: '/category/fruits',
      icon: '🍓'
    },
    {
      name: 'Tea & Spices',
      description: 'Aromatic herbs and spices for culinary delight',
      image: '/images/tea spices categories.jpg',
      path: '/category/tea-spices',
      icon: '🌿'
    },
    {
      name: 'Seedlings',
      description: 'Quality seedlings to start your own garden',
      image: '/images/seedling categories.jpg',
      path: '/category/seedlings',
      icon: '🌱'
    }
  ];

  const featuredProducts = [
    {
      name: 'Red Roses',
      description: 'Beautiful red roses perfect for gifts',
      image: '/images/red roses.jpeg',
      category: 'Flowers'
    },
    {
      name: 'Fresh Tomatoes',
      description: 'Juicy organic tomatoes',
      image: '/images/tomatoes.jpeg',
      category: 'Vegetables'
    },
    {
      name: 'Sweet Strawberries',
      description: 'Fresh strawberries bursting with flavor',
      image: '/images/strowberries.jpeg',
      category: 'Fruits'
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative h-screen flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 z-0">
          <img
            src="/images/mubwiza background image.png"
            alt="Mubwiza Garden"
            className="w-full h-full object-cover animate-zoom"
          />
          <div className="absolute inset-0 bg-gradient-to-br from-garden-primary/70 via-garden-secondary/60 to-garden-primary/80"></div>
        </div>

        <div className="relative z-10 text-center text-white px-6 max-w-5xl mx-auto">
          <div className={`${isVisible ? 'animate-fade-in-up' : 'opacity-0'}`}>
            <h1 className="font-dancing text-6xl md:text-7xl lg:text-8xl font-bold mb-6 drop-shadow-lg leading-tight">
              Welcome to <span className="bg-gradient-to-r from-garden-accent to-yellow-400 bg-clip-text text-transparent">Mubwiza Garden</span>
            </h1>
            <p className="text-xl md:text-2xl font-light mb-10 leading-relaxed drop-shadow-md max-w-4xl mx-auto" style={{animationDelay: '0.3s'}}>
              Where nature's beauty comes alive through our carefully cultivated
              flowers, fresh vegetables, seasonal fruits, aromatic spices, and quality seedlings.
            </p>
            <div className="flex flex-col sm:flex-row gap-6 justify-center" style={{animationDelay: '0.6s'}}>
              <Link
                to="#categories"
                className="bg-gradient-to-r from-garden-primary to-garden-secondary text-white px-8 py-4 rounded-full font-semibold hover:-translate-y-1 hover:shadow-2xl transition-all duration-300 inline-block flex items-center"
              >
                <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M17,8C8,10 5.9,16.17 3.82,21.34L5.71,22L6.66,19.7C7.14,19.87 7.64,20 8,20C19,20 22,3 22,3C21,5 14,5.25 9,6.25C4,7.25 2,11.5 2,13.5C2,15.5 3.75,17.25 3.75,17.25C7,8 17,8 17,8Z"/>
                </svg>
                Explore Our Garden
              </Link>
              <Link
                to="/about"
                className="bg-transparent border-2 border-white text-white px-8 py-4 rounded-full font-semibold hover:bg-white hover:text-garden-primary transition-all duration-300 inline-block flex items-center"
              >
                <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M11,9H13V7H11M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M11,17H13V11H11V17Z"/>
                </svg>
                Learn More
              </Link>
            </div>
          </div>
        </div>

        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce z-10">
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
          </svg>
        </div>
      </section>

      {/* Categories Section */}
      <section id="categories" className="py-20 bg-gradient-to-br from-garden-soft to-garden-cream relative">
        <div className="absolute inset-0 opacity-5">
          <img src="/images/mubwiza background image.png" alt="" className="w-full h-full object-cover" />
        </div>

        <div className="max-w-7xl mx-auto px-6 relative z-10">
          <h2 className="font-dancing text-5xl font-semibold text-garden-primary text-center mb-6 relative">
            Our Garden Categories
            <span className="absolute -bottom-3 left-1/2 transform -translate-x-1/2 w-20 h-1 bg-gradient-to-r from-garden-accent to-yellow-400 rounded"></span>
          </h2>
          <p className="text-xl text-gray-600 text-center mb-16 max-w-2xl mx-auto">
            Discover the diverse beauty and bounty of Mubwiza Garden
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-8">
            {categories.map((category, index) => (
              <Link
                key={category.name}
                to={category.path}
                className="group bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl hover:-translate-y-4 transition-all duration-500 cursor-pointer"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="relative h-64 overflow-hidden">
                  <img
                    src={category.image}
                    alt={category.name}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-garden-primary/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                    <span className="text-6xl animate-bounce-slow">{category.icon}</span>
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-semibold text-garden-primary mb-3 flex items-center">
                    <span className="mr-2">{category.icon}</span> {category.name}
                  </h3>
                  <p className="text-gray-600 leading-relaxed mb-4">{category.description}</p>
                  <div className="text-garden-secondary font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    Explore {category.name} →
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Products Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-6">
          <h2 className="font-dancing text-5xl font-semibold text-garden-primary text-center mb-6 relative">
            Featured from Our Garden
            <span className="absolute -bottom-3 left-1/2 transform -translate-x-1/2 w-20 h-1 bg-gradient-to-r from-garden-accent to-yellow-400 rounded"></span>
          </h2>
          <p className="text-xl text-gray-600 text-center mb-16 max-w-2xl mx-auto">
            Handpicked selections showcasing the best of what we grow
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10">
            {featuredProducts.map((product, index) => (
              <div
                key={product.name}
                className="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl hover:-translate-y-2 transition-all duration-300"
                style={{ animationDelay: `${index * 0.2}s` }}
              >
                <div className="relative h-72 overflow-hidden">
                  <img
                    src={product.image}
                    alt={product.name}
                    className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute top-4 right-4 bg-gradient-to-r from-garden-accent to-yellow-400 text-white px-3 py-1 rounded-full text-sm font-medium">
                    {product.category}
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-2xl font-semibold text-garden-primary mb-3">{product.name}</h3>
                  <p className="text-gray-600 leading-relaxed">{product.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* About Preview Section */}
      <section className="py-20 bg-gradient-to-br from-garden-cream to-garden-soft relative">
        <div className="absolute inset-0 opacity-5">
          <img src="/images/mubwiza background image.png" alt="" className="w-full h-full object-cover" />
        </div>

        <div className="max-w-7xl mx-auto px-6 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div className="space-y-6">
              <h2 className="font-dancing text-5xl font-semibold text-garden-primary mb-6 relative">
                Growing Beauty, Naturally
                <span className="absolute -bottom-3 left-0 w-20 h-1 bg-gradient-to-r from-garden-accent to-yellow-400 rounded"></span>
              </h2>
              <p className="text-lg text-gray-700 leading-relaxed">
                At Mubwiza Garden, we believe in the power of nature to bring joy,
                nourishment, and beauty to life. Our garden is a testament to sustainable
                farming practices and the careful cultivation of nature's finest offerings.
              </p>
              <p className="text-lg text-gray-700 leading-relaxed">
                From vibrant flowers that brighten any space to fresh vegetables that
                nourish the body, every plant in our garden is grown with love, care,
                and respect for the environment.
              </p>
              <Link
                to="/about"
                className="inline-block bg-gradient-to-r from-garden-primary to-garden-secondary text-white px-8 py-4 rounded-full font-semibold hover:-translate-y-1 hover:shadow-lg transition-all duration-300"
              >
                Our Story
              </Link>
            </div>
            <div className="grid grid-cols-2 gap-6">
              <img
                src="/images/flowers in garden in vase.jpeg"
                alt="Garden flowers"
                className="w-full h-64 object-cover rounded-2xl shadow-lg hover:scale-105 transition-transform duration-300 transform -translate-y-4"
              />
              <img
                src="/images/seedlings in the garden.jpeg"
                alt="Garden seedlings"
                className="w-full h-64 object-cover rounded-2xl shadow-lg hover:scale-105 transition-transform duration-300 transform translate-y-4"
              />
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;
