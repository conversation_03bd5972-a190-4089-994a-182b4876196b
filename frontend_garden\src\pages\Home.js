import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import './Home.css';

const Home = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const categories = [
    {
      name: 'Flowers',
      description: 'Beautiful fresh flowers for all occasions',
      image: '/images/flowers categories.jpg',
      path: '/category/flowers',
      icon: '🌹'
    },
    {
      name: 'Vegetables',
      description: 'Fresh, organic vegetables grown with care',
      image: '/images/vegetables categories.jpg',
      path: '/category/vegetables',
      icon: '🥕'
    },
    {
      name: 'Fruits',
      description: 'Sweet, seasonal fruits at peak ripeness',
      image: '/images/fruits categories.jpg',
      path: '/category/fruits',
      icon: '🍓'
    },
    {
      name: 'Tea & Spices',
      description: 'Aromatic herbs and spices for culinary delight',
      image: '/images/tea spices categories.jpg',
      path: '/category/tea-spices',
      icon: '🌿'
    },
    {
      name: 'Seedlings',
      description: 'Quality seedlings to start your own garden',
      image: '/images/seedling categories.jpg',
      path: '/category/seedlings',
      icon: '🌱'
    }
  ];

  const featuredProducts = [
    {
      name: 'Red Roses',
      description: 'Beautiful red roses perfect for gifts',
      image: '/images/red roses.jpeg',
      category: 'Flowers'
    },
    {
      name: 'Fresh Tomatoes',
      description: 'Juicy organic tomatoes',
      image: '/images/tomatoes.jpeg',
      category: 'Vegetables'
    },
    {
      name: 'Sweet Strawberries',
      description: 'Fresh strawberries bursting with flavor',
      image: '/images/strowberries.jpeg',
      category: 'Fruits'
    }
  ];

  return (
    <div className="home">
      {/* Hero Section */}
      <section className="hero">
        <div className="hero-background">
          <img 
            src="/images/mubwiza background image.png" 
            alt="Mubwiza Garden" 
            className="hero-image"
          />
          <div className="hero-overlay"></div>
        </div>
        
        <div className="hero-content">
          <div className="container">
            <div className={`hero-text ${isVisible ? 'fade-in-up' : ''}`}>
              <h1 className="hero-title">
                Welcome to <span className="highlight">Mubwiza Garden</span>
              </h1>
              <p className="hero-subtitle">
                Where nature's beauty comes alive through our carefully cultivated 
                flowers, fresh vegetables, seasonal fruits, aromatic spices, and quality seedlings.
              </p>
              <div className="hero-buttons">
                <Link to="#categories" className="btn btn-primary">
                  Explore Our Garden
                </Link>
                <Link to="/about" className="btn btn-secondary">
                  Learn More
                </Link>
              </div>
            </div>
          </div>
        </div>

        <div className="hero-scroll-indicator">
          <div className="scroll-arrow bounce">
            <span>↓</span>
          </div>
        </div>
      </section>

      {/* Categories Section */}
      <section id="categories" className="categories-section section">
        <div className="container">
          <h2 className="section-title fade-in-up">Our Garden Categories</h2>
          <p className="section-subtitle fade-in-up">
            Discover the diverse beauty and bounty of Mubwiza Garden
          </p>
          
          <div className="categories-grid">
            {categories.map((category, index) => (
              <Link 
                key={category.name}
                to={category.path}
                className={`category-card hover-lift ${isVisible ? 'fade-in-up' : ''}`}
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="category-image-container">
                  <img 
                    src={category.image} 
                    alt={category.name}
                    className="category-image"
                  />
                  <div className="category-overlay">
                    <span className="category-icon">{category.icon}</span>
                  </div>
                </div>
                <div className="category-content">
                  <h3 className="category-title">{category.name}</h3>
                  <p className="category-description">{category.description}</p>
                  <span className="category-link-text">
                    Explore {category.name} →
                  </span>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Products Section */}
      <section className="featured-section section">
        <div className="container">
          <h2 className="section-title">Featured from Our Garden</h2>
          <p className="section-subtitle">
            Handpicked selections showcasing the best of what we grow
          </p>
          
          <div className="featured-grid">
            {featuredProducts.map((product, index) => (
              <div 
                key={product.name}
                className={`featured-card card hover-lift ${isVisible ? 'fade-in-up' : ''}`}
                style={{ animationDelay: `${index * 0.2}s` }}
              >
                <div className="featured-image-container">
                  <img 
                    src={product.image} 
                    alt={product.name}
                    className="card-image"
                  />
                  <div className="featured-badge">
                    <span>{product.category}</span>
                  </div>
                </div>
                <div className="card-content">
                  <h3 className="card-title">{product.name}</h3>
                  <p className="card-description">{product.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* About Preview Section */}
      <section className="about-preview section">
        <div className="container">
          <div className="about-content">
            <div className="about-text">
              <h2 className="section-title">Growing Beauty, Naturally</h2>
              <p className="about-description">
                At Mubwiza Garden, we believe in the power of nature to bring joy, 
                nourishment, and beauty to life. Our garden is a testament to sustainable 
                farming practices and the careful cultivation of nature's finest offerings.
              </p>
              <p className="about-description">
                From vibrant flowers that brighten any space to fresh vegetables that 
                nourish the body, every plant in our garden is grown with love, care, 
                and respect for the environment.
              </p>
              <Link to="/about" className="btn btn-primary">
                Our Story
              </Link>
            </div>
            <div className="about-images">
              <img 
                src="/images/flowers in garden in vase.jpeg" 
                alt="Garden flowers"
                className="about-image hover-scale"
              />
              <img 
                src="/images/seedlings in the garden.jpeg" 
                alt="Garden seedlings"
                className="about-image hover-scale"
              />
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;
