import React from 'react';
import { Link } from 'react-router-dom';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-gradient-to-br from-garden-primary to-garden-secondary text-white relative overflow-hidden">
      <div className="absolute inset-0 opacity-10">
        <img src="/images/mubwiza background image.png" alt="" className="w-full h-full object-cover" />
      </div>

      <div className="max-w-7xl mx-auto px-6 py-16 relative z-10">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12 mb-12">
          <div className="lg:col-span-2">
            <div className="flex items-center font-dancing text-3xl font-semibold mb-6">
              <span className="text-4xl mr-3 animate-bounce-slow">🌱</span>
              Mubwiza Garden
            </div>
            <p className="text-lg mb-6 leading-relaxed opacity-90 max-w-md">
              Welcome to Mubwiza Garden, where nature's beauty comes alive.
              We showcase the finest flowers, fresh vegetables, seasonal fruits,
              aromatic tea & spices, and quality seedlings.
            </p>
            <div className="text-sm mb-8 opacity-80 leading-relaxed">
              <p className="flex items-center justify-start">
                <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12,11.5A2.5,2.5 0 0,1 9.5,9A2.5,2.5 0 0,1 12,6.5A2.5,2.5 0 0,1 14.5,9A2.5,2.5 0 0,1 12,11.5M12,2A7,7 0 0,0 5,9C5,14.25 12,22 12,22C12,22 19,14.25 19,9A7,7 0 0,0 12,2Z"/>
                </svg>
                Muhabura Integrated Polytechnic College (MIPC)
              </p>
              <p className="ml-6">Northern Province, Musanze, Muhoza, Rwanda</p>
            </div>
            <div className="flex space-x-4">
              <a
                href="#"
                className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center hover:bg-garden-accent hover:-translate-y-1 transition-all duration-300 backdrop-blur-sm"
                aria-label="Facebook"
              >
                <span className="text-xl">📘</span>
              </a>
              <a
                href="#"
                className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center hover:bg-garden-accent hover:-translate-y-1 transition-all duration-300 backdrop-blur-sm"
                aria-label="Instagram"
              >
                <span className="text-xl">📷</span>
              </a>
              <a
                href="#"
                className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center hover:bg-garden-accent hover:-translate-y-1 transition-all duration-300 backdrop-blur-sm"
                aria-label="Twitter"
              >
                <span className="text-xl">🐦</span>
              </a>
              <a
                href="#"
                className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center hover:bg-garden-accent hover:-translate-y-1 transition-all duration-300 backdrop-blur-sm"
                aria-label="WhatsApp"
              >
                <span className="text-xl">💬</span>
              </a>
            </div>
          </div>

          <div>
            <h3 className="text-xl font-semibold mb-6 text-garden-accent relative">
              Quick Links
              <span className="absolute -bottom-1 left-0 w-8 h-0.5 bg-garden-accent"></span>
            </h3>
            <ul className="space-y-3">
              <li>
                <Link
                  to="/"
                  className="text-white/90 hover:text-garden-accent transition-colors duration-300 relative pl-4 hover:pl-5"
                >
                  <span className="absolute left-0 opacity-0 hover:opacity-100 transition-opacity duration-300">🌿</span>
                  Home
                </Link>
              </li>
              <li>
                <Link
                  to="/about"
                  className="text-white/90 hover:text-garden-accent transition-colors duration-300 relative pl-4 hover:pl-5"
                >
                  <span className="absolute left-0 opacity-0 hover:opacity-100 transition-opacity duration-300">🌿</span>
                  About Us
                </Link>
              </li>
              <li>
                <Link
                  to="/contact"
                  className="text-white/90 hover:text-garden-accent transition-colors duration-300 relative pl-4 hover:pl-5"
                >
                  <span className="absolute left-0 opacity-0 hover:opacity-100 transition-opacity duration-300">🌿</span>
                  Contact
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="text-xl font-semibold mb-6 text-garden-accent relative">
              Categories
              <span className="absolute -bottom-1 left-0 w-8 h-0.5 bg-garden-accent"></span>
            </h3>
            <ul className="space-y-3">
              <li>
                <Link
                  to="/category/flowers"
                  className="text-white/90 hover:text-garden-accent transition-colors duration-300 relative pl-4 hover:pl-5"
                >
                  <span className="absolute left-0 opacity-0 hover:opacity-100 transition-opacity duration-300">🌿</span>
                  Flowers
                </Link>
              </li>
              <li>
                <Link
                  to="/category/vegetables"
                  className="text-white/90 hover:text-garden-accent transition-colors duration-300 relative pl-4 hover:pl-5"
                >
                  <span className="absolute left-0 opacity-0 hover:opacity-100 transition-opacity duration-300">🌿</span>
                  Vegetables
                </Link>
              </li>
              <li>
                <Link
                  to="/category/fruits"
                  className="text-white/90 hover:text-garden-accent transition-colors duration-300 relative pl-4 hover:pl-5"
                >
                  <span className="absolute left-0 opacity-0 hover:opacity-100 transition-opacity duration-300">🌿</span>
                  Fruits
                </Link>
              </li>
              <li>
                <Link
                  to="/category/tea-spices"
                  className="text-white/90 hover:text-garden-accent transition-colors duration-300 relative pl-4 hover:pl-5"
                >
                  <span className="absolute left-0 opacity-0 hover:opacity-100 transition-opacity duration-300">🌿</span>
                  Tea & Spices
                </Link>
              </li>
              <li>
                <Link
                  to="/category/seedlings"
                  className="text-white/90 hover:text-garden-accent transition-colors duration-300 relative pl-4 hover:pl-5"
                >
                  <span className="absolute left-0 opacity-0 hover:opacity-100 transition-opacity duration-300">🌿</span>
                  Seedlings
                </Link>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-white/20 pt-8">
          <div className="h-px bg-gradient-to-r from-transparent via-garden-accent to-transparent mb-6"></div>
          <div className="text-center">
            <p className="text-white/80 mb-2">&copy; {currentYear} Mubwiza Garden. All rights reserved.</p>
            <p className="text-garden-accent font-medium italic">🌿 Growing beauty, naturally 🌿</p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
