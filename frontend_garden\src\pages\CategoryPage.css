/* Category Hero Section */
.category-hero {
  position: relative;
  height: 70vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  margin-top: 80px;
}

.category-hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

.category-hero-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  animation: slowZoom 15s ease-in-out infinite alternate;
}

.category-hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(45, 80, 22, 0.8),
    rgba(74, 124, 89, 0.7)
  );
}

.category-hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  color: white;
  padding: 0 20px;
}

.category-breadcrumb {
  font-size: 1rem;
  margin-bottom: 20px;
  opacity: 0.9;
}

.category-breadcrumb a {
  color: var(--accent-gold);
  text-decoration: none;
  transition: opacity 0.3s ease;
}

.category-breadcrumb a:hover {
  opacity: 0.8;
}

.category-breadcrumb span {
  margin: 0 10px;
}

.category-icon-large {
  font-size: 5rem;
  margin-bottom: 20px;
  animation: bounce 2s infinite;
}

.category-hero-title {
  font-family: 'Dancing Script', cursive;
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 20px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.category-hero-description {
  font-size: 1.2rem;
  font-weight: 300;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

/* Products Section */
.category-products {
  background: white;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 40px;
}

.product-card {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.4s ease;
}

.product-image-container {
  height: 300px;
  overflow: hidden;
  position: relative;
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.product-card:hover .product-image {
  transform: scale(1.1);
}

.product-content {
  padding: 30px;
}

.product-title {
  font-size: 1.8rem;
  font-weight: 600;
  color: var(--primary-green);
  margin-bottom: 15px;
}

.product-description {
  color: var(--text-light);
  line-height: 1.6;
  margin-bottom: 25px;
  font-size: 1.1rem;
}

.product-features h4 {
  color: var(--primary-green);
  font-size: 1.1rem;
  margin-bottom: 15px;
  font-weight: 600;
}

.product-features ul {
  list-style: none;
  padding: 0;
}

.product-features li {
  position: relative;
  padding: 8px 0 8px 25px;
  color: var(--text-dark);
  font-weight: 500;
}

.product-features li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--secondary-green);
  font-weight: bold;
  font-size: 1.1rem;
}

/* Gallery Section */
.category-gallery {
  background: linear-gradient(135deg, var(--soft-white), var(--cream));
  position: relative;
}

.category-gallery::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('/images/mubwiza background image.png') center/cover;
  opacity: 0.03;
  z-index: 0;
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
  position: relative;
  z-index: 1;
}

.gallery-item {
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.gallery-image {
  width: 100%;
  height: 250px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.gallery-item:hover .gallery-image {
  transform: scale(1.05);
}

/* Other Categories Section */
.other-categories {
  background: white;
}

.other-categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 25px;
}

.other-category-card {
  position: relative;
  height: 200px;
  border-radius: 15px;
  overflow: hidden;
  text-decoration: none;
  color: white;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.other-category-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.other-category-card:hover .other-category-image {
  transform: scale(1.1);
}

.other-category-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(45, 80, 22, 0.8),
    rgba(74, 124, 89, 0.6)
  );
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  transition: background 0.3s ease;
}

.other-category-card:hover .other-category-content {
  background: linear-gradient(
    135deg,
    rgba(45, 80, 22, 0.9),
    rgba(74, 124, 89, 0.8)
  );
}

.other-category-icon {
  font-size: 3rem;
  margin-bottom: 10px;
  animation: bounce 2s infinite;
}

.other-category-title {
  font-size: 1.3rem;
  font-weight: 600;
  margin: 0;
}

/* Category Not Found */
.category-not-found {
  min-height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  margin-top: 80px;
}

.category-not-found h1 {
  font-size: 3rem;
  color: var(--primary-green);
  margin-bottom: 20px;
}

.category-not-found p {
  font-size: 1.2rem;
  color: var(--text-light);
  margin-bottom: 30px;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .products-grid {
    grid-template-columns: 1fr;
  }
  
  .category-hero-title {
    font-size: 3rem;
  }
}

@media (max-width: 768px) {
  .category-hero {
    height: 60vh;
  }
  
  .category-hero-title {
    font-size: 2.5rem;
  }
  
  .category-hero-description {
    font-size: 1.1rem;
  }
  
  .category-icon-large {
    font-size: 4rem;
  }
  
  .products-grid {
    gap: 30px;
  }
  
  .product-content {
    padding: 25px;
  }
  
  .gallery-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
  }
  
  .other-categories-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
  }
}

@media (max-width: 480px) {
  .category-hero-title {
    font-size: 2rem;
  }
  
  .category-hero-description {
    font-size: 1rem;
  }
  
  .product-title {
    font-size: 1.5rem;
  }
  
  .gallery-grid {
    grid-template-columns: 1fr;
  }
  
  .other-categories-grid {
    grid-template-columns: 1fr;
  }
}
