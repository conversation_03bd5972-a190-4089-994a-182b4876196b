<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mubwiza Garden - Nature's Beauty</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Dancing+Script:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            line-height: 1.6;
            color: #333;
            overflow-x: hidden;
        }

        :root {
            --primary-green: #2d5016;
            --secondary-green: #4a7c59;
            --light-green: #8fbc8f;
            --accent-gold: #daa520;
            --warm-brown: #8b4513;
            --cream: #f5f5dc;
            --soft-white: #fafafa;
            --text-dark: #2c3e50;
            --text-light: #7f8c8d;
        }

        /* Header */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 15px 0;
            border-bottom: 1px solid rgba(45, 80, 22, 0.1);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            display: flex;
            align-items: center;
            text-decoration: none;
            font-family: 'Dancing Script', cursive;
            font-size: 1.8rem;
            font-weight: 600;
            color: var(--primary-green);
        }

        .logo-icon {
            font-size: 2rem;
            margin-right: 10px;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        .nav {
            display: flex;
            gap: 30px;
        }

        .nav-link {
            text-decoration: none;
            color: var(--text-dark);
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-link:hover {
            color: var(--primary-green);
        }

        /* Hero Section */
        .hero {
            position: relative;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .hero-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 0;
        }

        .hero-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            animation: slowZoom 20s ease-in-out infinite alternate;
        }

        @keyframes slowZoom {
            0% { transform: scale(1); }
            100% { transform: scale(1.1); }
        }

        .hero-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(45, 80, 22, 0.7), rgba(74, 124, 89, 0.6), rgba(45, 80, 22, 0.8));
        }

        .hero-content {
            position: relative;
            z-index: 2;
            text-align: center;
            color: white;
            padding: 0 20px;
        }

        .hero-title {
            font-family: 'Dancing Script', cursive;
            font-size: 4rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            animation: fadeInUp 1s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .highlight {
            background: linear-gradient(135deg, var(--accent-gold), #ffd700);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-subtitle {
            font-size: 1.3rem;
            font-weight: 300;
            margin-bottom: 40px;
            max-width: 700px;
            margin-left: auto;
            margin-right: auto;
            line-height: 1.6;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .btn {
            display: inline-block;
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 500;
            text-decoration: none;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0 10px;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-green), var(--secondary-green));
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(45, 80, 22, 0.3);
        }

        .btn-secondary {
            background: transparent;
            color: white;
            border: 2px solid white;
        }

        .btn-secondary:hover {
            background: white;
            color: var(--primary-green);
        }

        /* Categories Section */
        .categories-section {
            padding: 80px 0;
            background: linear-gradient(135deg, var(--soft-white), var(--cream));
        }

        .section-title {
            font-family: 'Dancing Script', cursive;
            font-size: 3rem;
            font-weight: 600;
            color: var(--primary-green);
            text-align: center;
            margin-bottom: 20px;
        }

        .section-subtitle {
            font-size: 1.2rem;
            color: var(--text-light);
            text-align: center;
            margin-bottom: 60px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .categories-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }

        .category-card {
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.4s ease;
            text-decoration: none;
            color: inherit;
        }

        .category-card:hover {
            transform: translateY(-15px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .category-image {
            width: 100%;
            height: 250px;
            object-fit: cover;
            transition: transform 0.4s ease;
        }

        .category-card:hover .category-image {
            transform: scale(1.1);
        }

        .category-content {
            padding: 30px;
        }

        .category-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--primary-green);
            margin-bottom: 10px;
        }

        .category-description {
            color: var(--text-light);
            line-height: 1.6;
        }

        /* Footer */
        .footer {
            background: linear-gradient(135deg, var(--primary-green), var(--secondary-green));
            color: white;
            padding: 60px 0 20px;
            text-align: center;
        }

        .footer-logo {
            font-family: 'Dancing Script', cursive;
            font-size: 2rem;
            margin-bottom: 20px;
        }

        .footer-description {
            max-width: 600px;
            margin: 0 auto 30px;
            line-height: 1.6;
        }

        .footer-bottom {
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            padding-top: 20px;
            margin-top: 40px;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.8rem;
            }
            
            .nav {
                display: none;
            }
            
            .categories-grid {
                grid-template-columns: 1fr;
                gap: 25px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <a href="#" class="logo">
                    <span class="logo-icon">🌱</span>
                    <span>Mubwiza Garden</span>
                </a>
                <nav class="nav">
                    <a href="#" class="nav-link">Home</a>
                    <a href="#" class="nav-link">Categories</a>
                    <a href="#" class="nav-link">About</a>
                    <a href="#" class="nav-link">Contact</a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-background">
            <img src="images/mubwiza background image.png" alt="Mubwiza Garden" class="hero-image">
            <div class="hero-overlay"></div>
        </div>
        
        <div class="hero-content">
            <div class="container">
                <h1 class="hero-title">
                    Welcome to <span class="highlight">Mubwiza Garden</span>
                </h1>
                <p class="hero-subtitle">
                    Where nature's beauty comes alive through our carefully cultivated 
                    flowers, fresh vegetables, seasonal fruits, aromatic spices, and quality seedlings.
                </p>
                <div>
                    <a href="#categories" class="btn btn-primary">Explore Our Garden</a>
                    <a href="#" class="btn btn-secondary">Learn More</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Categories Section -->
    <section id="categories" class="categories-section">
        <div class="container">
            <h2 class="section-title">Our Garden Categories</h2>
            <p class="section-subtitle">
                Discover the diverse beauty and bounty of Mubwiza Garden
            </p>
            
            <div class="categories-grid">
                <a href="#" class="category-card">
                    <img src="images/flowers categories.jpg" alt="Flowers" class="category-image">
                    <div class="category-content">
                        <h3 class="category-title">🌹 Flowers</h3>
                        <p class="category-description">Beautiful fresh flowers for all occasions</p>
                    </div>
                </a>
                
                <a href="#" class="category-card">
                    <img src="images/vegetables categories.jpg" alt="Vegetables" class="category-image">
                    <div class="category-content">
                        <h3 class="category-title">🥕 Vegetables</h3>
                        <p class="category-description">Fresh, organic vegetables grown with care</p>
                    </div>
                </a>
                
                <a href="#" class="category-card">
                    <img src="images/fruits categories.jpg" alt="Fruits" class="category-image">
                    <div class="category-content">
                        <h3 class="category-title">🍓 Fruits</h3>
                        <p class="category-description">Sweet, seasonal fruits at peak ripeness</p>
                    </div>
                </a>
                
                <a href="#" class="category-card">
                    <img src="images/tea spices categories.jpg" alt="Tea & Spices" class="category-image">
                    <div class="category-content">
                        <h3 class="category-title">🌿 Tea & Spices</h3>
                        <p class="category-description">Aromatic herbs and spices for culinary delight</p>
                    </div>
                </a>
                
                <a href="#" class="category-card">
                    <img src="images/seedling categories.jpg" alt="Seedlings" class="category-image">
                    <div class="category-content">
                        <h3 class="category-title">🌱 Seedlings</h3>
                        <p class="category-description">Quality seedlings to start your own garden</p>
                    </div>
                </a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-logo">
                <span>🌱</span> Mubwiza Garden
            </div>
            <p class="footer-description">
                Welcome to Mubwiza Garden, where nature's beauty comes alive. 
                We showcase the finest flowers, fresh vegetables, seasonal fruits, 
                aromatic tea & spices, and quality seedlings.
            </p>
            <div class="footer-bottom">
                <p>&copy; 2025 Mubwiza Garden. All rights reserved.</p>
                <p>🌿 Growing beauty, naturally 🌿</p>
            </div>
        </div>
    </footer>
</body>
</html>
