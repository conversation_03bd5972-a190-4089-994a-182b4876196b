<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mubwiza Garden - Nature's Beauty</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Dancing+Script:wght@400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'garden': {
                            'primary': '#2d5016',
                            'secondary': '#4a7c59',
                            'light': '#8fbc8f',
                            'accent': '#daa520',
                            'cream': '#f5f5dc',
                            'soft': '#fafafa',
                        }
                    },
                    fontFamily: {
                        'dancing': ['Dancing Script', 'cursive'],
                        'poppins': ['Poppins', 'sans-serif'],
                    },
                    animation: {
                        'bounce-slow': 'bounce 2s infinite',
                        'zoom': 'zoom 20s ease-in-out infinite alternate',
                        'fade-in-up': 'fadeInUp 0.8s ease-out',
                    },
                    keyframes: {
                        zoom: {
                            '0%': { transform: 'scale(1)' },
                            '100%': { transform: 'scale(1.1)' },
                        },
                        fadeInUp: {
                            '0%': { opacity: '0', transform: 'translateY(30px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' },
                        },
                    }
                }
            }
        }
    </script>
    <style>
        .hero-bg {
            background: linear-gradient(135deg, rgba(45, 80, 22, 0.7), rgba(74, 124, 89, 0.6), rgba(45, 80, 22, 0.8));
        }
        .garden-gradient {
            background: linear-gradient(135deg, #2d5016, #4a7c59);
        }
        .accent-gradient {
            background: linear-gradient(135deg, #daa520, #ffd700);
        }
        .text-gradient {
            background: linear-gradient(135deg, #daa520, #ffd700);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
</head>
<body class="font-poppins overflow-x-hidden">
    <!-- Header -->
    <header class="fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-sm border-b border-garden-primary/10">
        <div class="max-w-7xl mx-auto px-6">
            <div class="flex items-center justify-between py-4">
                <a href="#" class="flex items-center font-dancing text-3xl font-semibold text-garden-primary hover:scale-105 transition-transform duration-300">
                    <span class="text-4xl mr-3 animate-bounce-slow">🌱</span>
                    <span class="text-gradient">Mubwiza Garden</span>
                </a>
                <nav class="hidden md:flex space-x-8">
                    <a href="#" class="text-gray-700 hover:text-garden-primary font-medium transition-colors duration-300 relative group">
                        Home
                        <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-garden-primary transition-all duration-300 group-hover:w-full"></span>
                    </a>
                    <a href="#categories" class="text-gray-700 hover:text-garden-primary font-medium transition-colors duration-300 relative group">
                        Categories
                        <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-garden-primary transition-all duration-300 group-hover:w-full"></span>
                    </a>
                    <a href="#" class="text-gray-700 hover:text-garden-primary font-medium transition-colors duration-300 relative group">
                        About
                        <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-garden-primary transition-all duration-300 group-hover:w-full"></span>
                    </a>
                    <a href="#" class="text-gray-700 hover:text-garden-primary font-medium transition-colors duration-300 relative group">
                        Contact
                        <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-garden-primary transition-all duration-300 group-hover:w-full"></span>
                    </a>
                </nav>
                <button class="md:hidden text-garden-primary">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="relative h-screen flex items-center justify-center overflow-hidden">
        <div class="absolute inset-0 z-0">
            <img src="images/mubwiza background image.png" alt="Mubwiza Garden" class="w-full h-full object-cover animate-zoom">
            <div class="absolute inset-0 hero-bg"></div>
        </div>
        
        <div class="relative z-10 text-center text-white px-6 max-w-4xl mx-auto">
            <h1 class="font-dancing text-6xl md:text-7xl font-bold mb-6 drop-shadow-lg animate-fade-in-up">
                Welcome to <span class="text-gradient">Mubwiza Garden</span>
            </h1>
            <p class="text-xl md:text-2xl font-light mb-10 leading-relaxed drop-shadow-md animate-fade-in-up" style="animation-delay: 0.3s;">
                Where nature's beauty comes alive through our carefully cultivated 
                flowers, fresh vegetables, seasonal fruits, aromatic spices, and quality seedlings.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center animate-fade-in-up" style="animation-delay: 0.6s;">
                <a href="#categories" class="garden-gradient text-white px-8 py-4 rounded-full font-semibold hover:-translate-y-1 hover:shadow-2xl transition-all duration-300">
                    Explore Our Garden
                </a>
                <a href="#" class="bg-transparent border-2 border-white text-white px-8 py-4 rounded-full font-semibold hover:bg-white hover:text-garden-primary transition-all duration-300">
                    Learn More
                </a>
            </div>
        </div>

        <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce">
            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
            </svg>
        </div>
    </section>

    <!-- Categories Section -->
    <section id="categories" class="py-20 bg-gradient-to-br from-garden-soft to-garden-cream relative">
        <div class="absolute inset-0 opacity-5">
            <img src="images/mubwiza background image.png" alt="" class="w-full h-full object-cover">
        </div>
        
        <div class="max-w-7xl mx-auto px-6 relative z-10">
            <h2 class="font-dancing text-5xl font-semibold text-garden-primary text-center mb-6 relative">
                Our Garden Categories
                <span class="absolute -bottom-3 left-1/2 transform -translate-x-1/2 w-20 h-1 accent-gradient rounded"></span>
            </h2>
            <p class="text-xl text-gray-600 text-center mb-16 max-w-2xl mx-auto">
                Discover the diverse beauty and bounty of Mubwiza Garden
            </p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-8">
                <!-- Flowers -->
                <div class="group bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl hover:-translate-y-4 transition-all duration-500 cursor-pointer">
                    <div class="relative h-64 overflow-hidden">
                        <img src="images/flowers categories.jpg" alt="Flowers" class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">
                        <div class="absolute inset-0 bg-gradient-to-t from-garden-primary/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                            <span class="text-6xl animate-bounce-slow">🌹</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-garden-primary mb-3 flex items-center">
                            <span class="mr-2">🌹</span> Flowers
                        </h3>
                        <p class="text-gray-600 leading-relaxed">Beautiful fresh flowers for all occasions</p>
                        <div class="mt-4 text-garden-secondary font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            Explore Flowers →
                        </div>
                    </div>
                </div>

                <!-- Vegetables -->
                <div class="group bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl hover:-translate-y-4 transition-all duration-500 cursor-pointer">
                    <div class="relative h-64 overflow-hidden">
                        <img src="images/vegetables categories.jpg" alt="Vegetables" class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">
                        <div class="absolute inset-0 bg-gradient-to-t from-garden-primary/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                            <span class="text-6xl animate-bounce-slow">🥕</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-garden-primary mb-3 flex items-center">
                            <span class="mr-2">🥕</span> Vegetables
                        </h3>
                        <p class="text-gray-600 leading-relaxed">Fresh, organic vegetables grown with care</p>
                        <div class="mt-4 text-garden-secondary font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            Explore Vegetables →
                        </div>
                    </div>
                </div>

                <!-- Fruits -->
                <div class="group bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl hover:-translate-y-4 transition-all duration-500 cursor-pointer">
                    <div class="relative h-64 overflow-hidden">
                        <img src="images/fruits categories.jpg" alt="Fruits" class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">
                        <div class="absolute inset-0 bg-gradient-to-t from-garden-primary/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                            <span class="text-6xl animate-bounce-slow">🍓</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-garden-primary mb-3 flex items-center">
                            <span class="mr-2">🍓</span> Fruits
                        </h3>
                        <p class="text-gray-600 leading-relaxed">Sweet, seasonal fruits at peak ripeness</p>
                        <div class="mt-4 text-garden-secondary font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            Explore Fruits →
                        </div>
                    </div>
                </div>

                <!-- Tea & Spices -->
                <div class="group bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl hover:-translate-y-4 transition-all duration-500 cursor-pointer">
                    <div class="relative h-64 overflow-hidden">
                        <img src="images/tea spices categories.jpg" alt="Tea & Spices" class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">
                        <div class="absolute inset-0 bg-gradient-to-t from-garden-primary/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                            <span class="text-6xl animate-bounce-slow">🌿</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-garden-primary mb-3 flex items-center">
                            <span class="mr-2">🌿</span> Tea & Spices
                        </h3>
                        <p class="text-gray-600 leading-relaxed">Aromatic herbs and spices for culinary delight</p>
                        <div class="mt-4 text-garden-secondary font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            Explore Tea & Spices →
                        </div>
                    </div>
                </div>

                <!-- Seedlings -->
                <div class="group bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl hover:-translate-y-4 transition-all duration-500 cursor-pointer">
                    <div class="relative h-64 overflow-hidden">
                        <img src="images/seedling categories.jpg" alt="Seedlings" class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">
                        <div class="absolute inset-0 bg-gradient-to-t from-garden-primary/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                            <span class="text-6xl animate-bounce-slow">🌱</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-garden-primary mb-3 flex items-center">
                            <span class="mr-2">🌱</span> Seedlings
                        </h3>
                        <p class="text-gray-600 leading-relaxed">Quality seedlings to start your own garden</p>
                        <div class="mt-4 text-garden-secondary font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            Explore Seedlings →
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Products Section -->
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-6">
            <h2 class="font-dancing text-5xl font-semibold text-garden-primary text-center mb-6 relative">
                Featured from Our Garden
                <span class="absolute -bottom-3 left-1/2 transform -translate-x-1/2 w-20 h-1 accent-gradient rounded"></span>
            </h2>
            <p class="text-xl text-gray-600 text-center mb-16 max-w-2xl mx-auto">
                Handpicked selections showcasing the best of what we grow
            </p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10">
                <!-- Red Roses -->
                <div class="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl hover:-translate-y-2 transition-all duration-300">
                    <div class="relative h-72 overflow-hidden">
                        <img src="images/red roses.jpeg" alt="Red Roses" class="w-full h-full object-cover hover:scale-105 transition-transform duration-300">
                        <div class="absolute top-4 right-4 accent-gradient text-white px-3 py-1 rounded-full text-sm font-medium">
                            Flowers
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-2xl font-semibold text-garden-primary mb-3">Red Roses</h3>
                        <p class="text-gray-600 leading-relaxed">Beautiful red roses perfect for gifts and expressing love</p>
                    </div>
                </div>

                <!-- Fresh Tomatoes -->
                <div class="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl hover:-translate-y-2 transition-all duration-300">
                    <div class="relative h-72 overflow-hidden">
                        <img src="images/tomatoes.jpeg" alt="Fresh Tomatoes" class="w-full h-full object-cover hover:scale-105 transition-transform duration-300">
                        <div class="absolute top-4 right-4 accent-gradient text-white px-3 py-1 rounded-full text-sm font-medium">
                            Vegetables
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-2xl font-semibold text-garden-primary mb-3">Fresh Tomatoes</h3>
                        <p class="text-gray-600 leading-relaxed">Juicy organic tomatoes grown with sustainable methods</p>
                    </div>
                </div>

                <!-- Sweet Strawberries -->
                <div class="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl hover:-translate-y-2 transition-all duration-300">
                    <div class="relative h-72 overflow-hidden">
                        <img src="images/strowberries.jpeg" alt="Sweet Strawberries" class="w-full h-full object-cover hover:scale-105 transition-transform duration-300">
                        <div class="absolute top-4 right-4 accent-gradient text-white px-3 py-1 rounded-full text-sm font-medium">
                            Fruits
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-2xl font-semibold text-garden-primary mb-3">Sweet Strawberries</h3>
                        <p class="text-gray-600 leading-relaxed">Fresh strawberries bursting with natural flavor and vitamins</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="garden-gradient text-white relative overflow-hidden">
        <div class="absolute inset-0 opacity-10">
            <img src="images/mubwiza background image.png" alt="" class="w-full h-full object-cover">
        </div>
        
        <div class="max-w-7xl mx-auto px-6 py-16 relative z-10">
            <div class="text-center">
                <div class="font-dancing text-3xl font-semibold mb-6 flex items-center justify-center">
                    <span class="text-4xl mr-3 animate-bounce-slow">🌱</span>
                    Mubwiza Garden
                </div>
                <p class="text-lg mb-6 max-w-2xl mx-auto leading-relaxed opacity-90">
                    Welcome to Mubwiza Garden, where nature's beauty comes alive.
                    We showcase the finest flowers, fresh vegetables, seasonal fruits,
                    aromatic tea & spices, and quality seedlings.
                </p>
                <div class="text-sm mb-8 opacity-80">
                    <p class="font-semibold">📍 Muhabura Integrated Polytechnic College (MIPC)</p>
                    <p>Northern Province, Musanze, Muhoza, Rwanda</p>
                </div>
                
                <div class="flex justify-center space-x-6 mb-8">
                    <a href="#" class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center hover:bg-garden-accent hover:-translate-y-1 transition-all duration-300">
                        <span class="text-xl">📘</span>
                    </a>
                    <a href="#" class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center hover:bg-garden-accent hover:-translate-y-1 transition-all duration-300">
                        <span class="text-xl">📷</span>
                    </a>
                    <a href="#" class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center hover:bg-garden-accent hover:-translate-y-1 transition-all duration-300">
                        <span class="text-xl">💬</span>
                    </a>
                    <a href="#" class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center hover:bg-garden-accent hover:-translate-y-1 transition-all duration-300">
                        <span class="text-xl">🐦</span>
                    </a>
                </div>
                
                <div class="border-t border-white/20 pt-8">
                    <p class="opacity-80">&copy; 2025 Mubwiza Garden. All rights reserved.</p>
                    <p class="text-garden-accent font-medium mt-2">🌿 Growing beauty, naturally 🌿</p>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add scroll animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe elements for animation
        document.querySelectorAll('.group').forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(30px)';
            el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(el);
        });
    </script>
</body>
</html>
