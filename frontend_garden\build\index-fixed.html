<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mubwiza Garden - Nature's Beauty</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="favicon.svg">
    <link rel="icon" type="image/x-icon" href="favicon.ico">
    <link rel="apple-touch-icon" href="apple-touch-icon.png">
    <meta name="theme-color" content="#2d5016">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Dancing+Script:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-green: #2d5016;
            --secondary-green: #4a7c59;
            --light-green: #8fbc8f;
            --accent-gold: #daa520;
            --warm-brown: #8b4513;
            --cream: #f5f5dc;
            --soft-white: #fafafa;
            --text-dark: #2c3e50;
            --text-light: #7f8c8d;
        }

        body {
            font-family: 'Poppins', sans-serif;
            line-height: 1.6;
            color: #333;
            overflow-x: hidden;
            background-color: #f9f9f9;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 15px 0;
            border-bottom: 1px solid rgba(45, 80, 22, 0.1);
            transition: all 0.3s ease;
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            display: flex;
            align-items: center;
            text-decoration: none;
            font-family: 'Dancing Script', cursive;
            font-size: 2rem;
            font-weight: 600;
            color: var(--primary-green);
            transition: transform 0.3s ease;
        }

        .logo:hover {
            transform: scale(1.05);
        }

        .logo-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--primary-green), var(--secondary-green));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: white;
            font-size: 1.5rem;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        .nav {
            display: flex;
            gap: 30px;
        }

        .nav-link {
            text-decoration: none;
            color: var(--text-dark);
            font-weight: 500;
            transition: color 0.3s ease;
            position: relative;
        }

        .nav-link:hover {
            color: var(--primary-green);
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 0;
            height: 2px;
            background: linear-gradient(135deg, var(--accent-gold), #ffd700);
            transition: width 0.3s ease;
        }

        .nav-link:hover::after {
            width: 100%;
        }

        /* Hero Section */
        .hero {
            position: relative;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .hero-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 0;
        }

        .hero-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            animation: slowZoom 20s ease-in-out infinite alternate;
        }

        @keyframes slowZoom {
            0% { transform: scale(1); }
            100% { transform: scale(1.1); }
        }

        .hero-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(45, 80, 22, 0.7), rgba(74, 124, 89, 0.6), rgba(45, 80, 22, 0.8));
        }

        .hero-content {
            position: relative;
            z-index: 2;
            text-align: center;
            color: white;
            padding: 0 20px;
            max-width: 1000px;
        }

        .hero-title {
            font-family: 'Dancing Script', cursive;
            font-size: 4rem;
            font-weight: 700;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            animation: fadeInUp 1s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .highlight {
            background: linear-gradient(135deg, var(--accent-gold), #ffd700);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-subtitle {
            font-size: 1.3rem;
            font-weight: 300;
            margin-bottom: 40px;
            line-height: 1.6;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .hero-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
            animation: fadeInUp 1s ease-out 0.6s both;
        }

        .btn {
            display: inline-block;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 500;
            text-decoration: none;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-green), var(--secondary-green));
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(45, 80, 22, 0.3);
        }

        .btn-secondary {
            background: transparent;
            color: white;
            border: 2px solid white;
        }

        .btn-secondary:hover {
            background: white;
            color: var(--primary-green);
        }

        /* Categories Section */
        .categories-section {
            padding: 80px 0;
            background: linear-gradient(135deg, var(--soft-white), var(--cream));
            position: relative;
        }

        .categories-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('images/mubwiza background image.png') center/cover;
            opacity: 0.03;
            z-index: 0;
        }

        .section-title {
            font-family: 'Dancing Script', cursive;
            font-size: 3.5rem;
            font-weight: 600;
            color: var(--primary-green);
            text-align: center;
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(135deg, var(--accent-gold), #ffd700);
            border-radius: 2px;
        }

        .section-subtitle {
            font-size: 1.2rem;
            color: var(--text-light);
            text-align: center;
            margin-bottom: 60px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            position: relative;
            z-index: 1;
        }

        .categories-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            position: relative;
            z-index: 1;
        }

        .category-card {
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.4s ease;
            text-decoration: none;
            color: inherit;
        }

        .category-card:hover {
            transform: translateY(-15px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .category-image-container {
            position: relative;
            height: 250px;
            overflow: hidden;
        }

        .category-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.4s ease;
        }

        .category-card:hover .category-image {
            transform: scale(1.1);
        }

        .category-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(to top, rgba(45, 80, 22, 0.8), transparent);
            opacity: 0;
            transition: opacity 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .category-card:hover .category-overlay {
            opacity: 1;
        }

        .category-icon {
            font-size: 4rem;
            color: white;
            animation: bounce 2s infinite;
        }

        .category-content {
            padding: 30px;
        }

        .category-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--primary-green);
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }

        .category-title-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: white;
            font-size: 1.2rem;
        }

        .category-description {
            color: var(--text-light);
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .category-link-text {
            color: var(--secondary-green);
            font-weight: 500;
            opacity: 0;
            transform: translateX(10px);
            transition: all 0.3s ease;
        }

        .category-card:hover .category-link-text {
            opacity: 1;
            transform: translateX(0);
        }

        /* Footer */
        .footer {
            background: linear-gradient(135deg, var(--primary-green), var(--secondary-green));
            color: white;
            padding: 60px 0 20px;
            text-align: center;
            position: relative;
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('images/mubwiza background image.png') center/cover;
            opacity: 0.1;
            z-index: 0;
        }

        .footer-content {
            position: relative;
            z-index: 1;
        }

        .footer-logo {
            font-family: 'Dancing Script', cursive;
            font-size: 2.5rem;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .footer-logo-icon {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 1.8rem;
            animation: bounce 2s infinite;
        }

        .footer-description {
            max-width: 600px;
            margin: 0 auto 30px;
            line-height: 1.6;
            opacity: 0.9;
        }

        .footer-address {
            margin: 20px auto;
            font-size: 0.95rem;
            opacity: 0.8;
            line-height: 1.5;
        }

        .footer-bottom {
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            padding-top: 20px;
            margin-top: 40px;
        }

        .footer-tagline {
            color: var(--accent-gold);
            font-weight: 500;
            margin-top: 10px;
            font-style: italic;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.8rem;
            }
            
            .nav {
                display: none;
            }
            
            .categories-grid {
                grid-template-columns: 1fr;
                gap: 25px;
            }
            
            .hero-buttons {
                flex-direction: column;
                align-items: center;
            }
        }

        @media (max-width: 480px) {
            .hero-title {
                font-size: 2.2rem;
            }
            
            .hero-subtitle {
                font-size: 1.1rem;
            }
            
            .section-title {
                font-size: 2.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <a href="#" class="logo">
                    <div class="logo-icon">🌱</div>
                    <span>Mubwiza Garden</span>
                </a>
                <nav class="nav">
                    <a href="#" class="nav-link">Home</a>
                    <a href="#categories" class="nav-link">Categories</a>
                    <a href="#about" class="nav-link">About</a>
                    <a href="#contact" class="nav-link">Contact</a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-background">
            <img src="images/mubwiza background image.png" alt="Mubwiza Garden" class="hero-image">
            <div class="hero-overlay"></div>
        </div>
        
        <div class="hero-content">
            <h1 class="hero-title">
                Welcome to <span class="highlight">Mubwiza Garden</span>
            </h1>
            <p class="hero-subtitle">
                Where nature's beauty comes alive through our carefully cultivated 
                flowers, fresh vegetables, seasonal fruits, aromatic spices, and quality seedlings.
            </p>
            <div class="hero-buttons">
                <a href="#categories" class="btn btn-primary">Explore Our Garden</a>
                <a href="#about" class="btn btn-secondary">Learn More</a>
            </div>
        </div>
    </section>

    <!-- Categories Section -->
    <section id="categories" class="categories-section">
        <div class="container">
            <h2 class="section-title">Our Garden Categories</h2>
            <p class="section-subtitle">
                Discover the diverse beauty and bounty of Mubwiza Garden
            </p>

            <div class="categories-grid">
                <!-- Flowers -->
                <a href="#" class="category-card">
                    <div class="category-image-container">
                        <img src="images/flowers categories.jpg" alt="Flowers" class="category-image">
                        <div class="category-overlay">
                            <div class="category-icon">🌹</div>
                        </div>
                    </div>
                    <div class="category-content">
                        <h3 class="category-title">
                            <div class="category-title-icon" style="background: linear-gradient(135deg, #f472b6, #ec4899);">🌹</div>
                            Flowers
                        </h3>
                        <p class="category-description">Beautiful fresh flowers for all occasions - decorations, gifts, and special events</p>
                        <div class="category-link-text">Explore Flowers →</div>
                    </div>
                </a>

                <!-- Vegetables -->
                <a href="#" class="category-card">
                    <div class="category-image-container">
                        <img src="images/vegetables categories.jpg" alt="Vegetables" class="category-image">
                        <div class="category-overlay">
                            <div class="category-icon">🥕</div>
                        </div>
                    </div>
                    <div class="category-content">
                        <h3 class="category-title">
                            <div class="category-title-icon" style="background: linear-gradient(135deg, #4ade80, #10b981);">🥕</div>
                            Vegetables
                        </h3>
                        <p class="category-description">Fresh, organic vegetables grown with sustainable farming practices</p>
                        <div class="category-link-text">Explore Vegetables →</div>
                    </div>
                </a>

                <!-- Fruits -->
                <a href="#" class="category-card">
                    <div class="category-image-container">
                        <img src="images/fruits categories.jpg" alt="Fruits" class="category-image">
                        <div class="category-overlay">
                            <div class="category-icon">🍓</div>
                        </div>
                    </div>
                    <div class="category-content">
                        <h3 class="category-title">
                            <div class="category-title-icon" style="background: linear-gradient(135deg, #f87171, #ec4899);">🍓</div>
                            Fruits
                        </h3>
                        <p class="category-description">Sweet, seasonal fruits harvested at peak ripeness for maximum flavor</p>
                        <div class="category-link-text">Explore Fruits →</div>
                    </div>
                </a>

                <!-- Tea & Spices -->
                <a href="#" class="category-card">
                    <div class="category-image-container">
                        <img src="images/tea spices categories.jpg" alt="Tea & Spices" class="category-image">
                        <div class="category-overlay">
                            <div class="category-icon">🌿</div>
                        </div>
                    </div>
                    <div class="category-content">
                        <h3 class="category-title">
                            <div class="category-title-icon" style="background: linear-gradient(135deg, #34d399, #14b8a6);">🌿</div>
                            Tea & Spices
                        </h3>
                        <p class="category-description">Aromatic herbs and spices to enhance your culinary experience</p>
                        <div class="category-link-text">Explore Tea & Spices →</div>
                    </div>
                </a>

                <!-- Seedlings -->
                <a href="#" class="category-card">
                    <div class="category-image-container">
                        <img src="images/seedling categories.jpg" alt="Seedlings" class="category-image">
                        <div class="category-overlay">
                            <div class="category-icon">🌱</div>
                        </div>
                    </div>
                    <div class="category-content">
                        <h3 class="category-title">
                            <div class="category-title-icon" style="background: linear-gradient(135deg, #84cc16, #22c55e);">🌱</div>
                            Seedlings
                        </h3>
                        <p class="category-description">Quality seedlings to help you start your own beautiful garden</p>
                        <div class="category-link-text">Explore Seedlings →</div>
                    </div>
                </a>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" style="padding: 80px 0; background: white;">
        <div class="container">
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 60px; align-items: center;">
                <div>
                    <h2 class="section-title" style="text-align: left; margin-bottom: 30px;">Growing Beauty, Naturally</h2>
                    <p style="font-size: 1.2rem; color: #374151; line-height: 1.6; margin-bottom: 20px;">
                        At Mubwiza Garden, we believe in the power of nature to bring joy,
                        nourishment, and beauty to life. Our garden is a testament to sustainable
                        farming practices and the careful cultivation of nature's finest offerings.
                    </p>
                    <p style="font-size: 1.1rem; color: #6b7280; line-height: 1.6; margin-bottom: 30px;">
                        From vibrant flowers that brighten any space to fresh vegetables that
                        nourish the body, every plant in our garden is grown with love, care,
                        and respect for the environment.
                    </p>
                    <div style="display: flex; flex-wrap: wrap; gap: 20px; margin-bottom: 30px;">
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <div style="width: 50px; height: 50px; background: linear-gradient(135deg, #4ade80, #10b981); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.2rem;">🌱</div>
                            <span style="font-weight: 600; color: var(--primary-green);">100% Organic</span>
                        </div>
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <div style="width: 50px; height: 50px; background: linear-gradient(135deg, #34d399, #14b8a6); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.2rem;">♻️</div>
                            <span style="font-weight: 600; color: var(--primary-green);">Sustainable</span>
                        </div>
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <div style="width: 50px; height: 50px; background: linear-gradient(135deg, #06b6d4, #0891b2); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.2rem;">💚</div>
                            <span style="font-weight: 600; color: var(--primary-green);">Eco-Friendly</span>
                        </div>
                    </div>
                    <a href="#contact" class="btn btn-primary">Visit Our Garden</a>
                </div>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <img src="images/flowers in garden in vase.jpeg" alt="Garden flowers" style="width: 100%; height: 300px; object-fit: cover; border-radius: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); transform: translateY(-20px);">
                    <img src="images/seedlings in the garden.jpeg" alt="Garden seedlings" style="width: 100%; height: 300px; object-fit: cover; border-radius: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); transform: translateY(20px);">
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer id="contact" class="footer">
        <div class="footer-content">
            <div class="container">
                <div class="footer-logo">
                    <div class="footer-logo-icon">🌱</div>
                    Mubwiza Garden
                </div>
                <p class="footer-description">
                    Welcome to Mubwiza Garden, where nature's beauty comes alive.
                    We showcase the finest flowers, fresh vegetables, seasonal fruits,
                    aromatic tea & spices, and quality seedlings.
                </p>
                <div class="footer-address">
                    <p><strong>📍 Muhabura Integrated Polytechnic College (MIPC)</strong></p>
                    <p>Northern Province, Musanze, Muhoza, Rwanda</p>
                </div>

                <div class="footer-bottom">
                    <p>&copy; 2025 Mubwiza Garden. All rights reserved.</p>
                    <p class="footer-tagline">🌿 Growing beauty, naturally 🌿</p>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Header background on scroll
        window.addEventListener('scroll', () => {
            const header = document.querySelector('.header');
            if (window.scrollY > 100) {
                header.style.background = 'rgba(255, 255, 255, 0.98)';
                header.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
            } else {
                header.style.background = 'rgba(255, 255, 255, 0.95)';
                header.style.boxShadow = 'none';
            }
        });
    </script>
</body>
</html>
