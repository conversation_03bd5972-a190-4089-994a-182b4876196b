/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
    "./public/index.html",
  ],
  theme: {
    extend: {
      colors: {
        'garden': {
          'primary': '#2d5016',
          'secondary': '#4a7c59',
          'light': '#8fbc8f',
          'accent': '#daa520',
          'cream': '#f5f5dc',
          'soft': '#fafafa',
        },
        'text': {
          'dark': '#2c3e50',
          'light': '#7f8c8d',
        }
      },
      fontFamily: {
        'dancing': ['Dancing Script', 'cursive'],
        'poppins': ['Poppins', 'sans-serif'],
      },
      animation: {
        'bounce-slow': 'bounce 2s infinite',
        'pulse-slow': 'pulse 2s infinite',
        'zoom': 'zoom 20s ease-in-out infinite alternate',
        'fade-in-up': 'fadeInUp 0.8s ease-out',
        'slide-in-left': 'slideInLeft 0.8s ease-out',
        'slide-in-right': 'slideInRight 0.8s ease-out',
      },
      keyframes: {
        zoom: {
          '0%': { transform: 'scale(1)' },
          '100%': { transform: 'scale(1.1)' },
        },
        fadeInUp: {
          '0%': { opacity: '0', transform: 'translateY(30px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        slideInLeft: {
          '0%': { opacity: '0', transform: 'translateX(-50px)' },
          '100%': { opacity: '1', transform: 'translateX(0)' },
        },
        slideInRight: {
          '0%': { opacity: '0', transform: 'translateX(50px)' },
          '100%': { opacity: '1', transform: 'translateX(0)' },
        },
      },
      backgroundImage: {
        'garden-gradient': 'linear-gradient(135deg, #2d5016, #4a7c59)',
        'hero-overlay': 'linear-gradient(135deg, rgba(45, 80, 22, 0.7), rgba(74, 124, 89, 0.6), rgba(45, 80, 22, 0.8))',
        'accent-gradient': 'linear-gradient(135deg, #daa520, #ffd700)',
      },
      boxShadow: {
        'garden': '0 10px 30px rgba(0, 0, 0, 0.1)',
        'garden-hover': '0 20px 40px rgba(0, 0, 0, 0.15)',
        'garden-lg': '0 15px 35px rgba(0, 0, 0, 0.1)',
      },
      backdropBlur: {
        'xs': '2px',
      }
    },
  },
  plugins: [],
}
