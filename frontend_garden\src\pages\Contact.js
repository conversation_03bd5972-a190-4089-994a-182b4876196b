import React, { useEffect, useState } from 'react';
import './Contact.css';

const Contact = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
    window.scrollTo(0, 0);
  }, []);

  const contactInfo = [
    {
      icon: '📍',
      title: 'Location',
      details: ['Mubwiza Garden', 'Rwanda'],
      description: 'Visit our beautiful garden and experience nature firsthand'
    },
    {
      icon: '📞',
      title: 'Phone',
      details: ['+250 XXX XXX XXX'],
      description: 'Call us for inquiries and garden visits'
    },
    {
      icon: '✉️',
      title: 'Email',
      details: ['<EMAIL>'],
      description: 'Send us a message and we\'ll get back to you'
    },
    {
      icon: '🕒',
      title: 'Hours',
      details: ['Mon - Sat: 8:00 AM - 6:00 PM', 'Sunday: Closed'],
      description: 'Best times to visit our garden'
    }
  ];

  const socialLinks = [
    {
      name: 'Facebook',
      icon: '📘',
      url: '#',
      description: 'Follow us for daily garden updates'
    },
    {
      name: 'Instagram',
      icon: '📷',
      url: '#',
      description: 'See beautiful photos of our garden'
    },
    {
      name: 'WhatsApp',
      icon: '💬',
      url: '#',
      description: 'Quick messages and inquiries'
    },
    {
      name: 'Twitter',
      icon: '🐦',
      url: '#',
      description: 'Latest news and garden tips'
    }
  ];

  const visitingTips = [
    {
      icon: '🌅',
      title: 'Best Time to Visit',
      description: 'Early morning (8-10 AM) for the freshest experience and cooler weather'
    },
    {
      icon: '👕',
      title: 'What to Wear',
      description: 'Comfortable walking shoes and light, breathable clothing'
    },
    {
      icon: '📸',
      title: 'Photography',
      description: 'Feel free to take photos of our beautiful plants and flowers'
    },
    {
      icon: '🎒',
      title: 'What to Bring',
      description: 'Water bottle, hat, and a bag for any purchases you might make'
    }
  ];

  return (
    <div className="contact-page">
      {/* Hero Section */}
      <section className="contact-hero">
        <div className="contact-hero-background">
          <img 
            src="/images/mubwiza background image.png" 
            alt="Contact Mubwiza Garden"
            className="contact-hero-image"
          />
          <div className="contact-hero-overlay"></div>
        </div>
        
        <div className="contact-hero-content">
          <div className="container">
            <div className={`contact-hero-text ${isVisible ? 'fade-in-up' : ''}`}>
              <h1 className="contact-hero-title">Visit Mubwiza Garden</h1>
              <p className="contact-hero-subtitle">
                We'd love to welcome you to our beautiful garden. 
                Come experience the beauty of nature with us.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Info Section */}
      <section className="contact-info section">
        <div className="container">
          <h2 className="section-title">Get In Touch</h2>
          <p className="section-subtitle">
            Here's how you can reach us and plan your visit
          </p>
          
          <div className="contact-grid">
            {contactInfo.map((info, index) => (
              <div 
                key={info.title}
                className={`contact-card hover-lift ${isVisible ? 'fade-in-up' : ''}`}
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="contact-icon">{info.icon}</div>
                <h3 className="contact-title">{info.title}</h3>
                <div className="contact-details">
                  {info.details.map((detail, idx) => (
                    <p key={idx} className="contact-detail">{detail}</p>
                  ))}
                </div>
                <p className="contact-description">{info.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Map Section */}
      <section className="contact-map section">
        <div className="container">
          <h2 className="section-title">Find Us</h2>
          <div className="map-container">
            <div className="map-placeholder">
              <div className="map-content">
                <div className="map-icon">🗺️</div>
                <h3>Mubwiza Garden Location</h3>
                <p>Rwanda</p>
                <p className="map-note">
                  Contact us for detailed directions to our garden location
                </p>
              </div>
            </div>
            <div className="map-info">
              <h3>Directions</h3>
              <p>
                Our garden is located in a beautiful area of Rwanda, surrounded by 
                natural beauty. We're easily accessible and provide a peaceful 
                environment for visitors to enjoy.
              </p>
              <div className="direction-tips">
                <h4>Getting Here:</h4>
                <ul>
                  <li>Contact us for specific directions</li>
                  <li>Public transportation available</li>
                  <li>Parking available on-site</li>
                  <li>Wheelchair accessible paths</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Social Media Section */}
      <section className="contact-social section">
        <div className="container">
          <h2 className="section-title">Connect With Us</h2>
          <p className="section-subtitle">
            Follow us on social media for garden updates, tips, and beautiful photos
          </p>
          
          <div className="social-grid">
            {socialLinks.map((social, index) => (
              <a 
                key={social.name}
                href={social.url}
                className={`social-card hover-lift ${isVisible ? 'fade-in-up' : ''}`}
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="social-icon">{social.icon}</div>
                <h3 className="social-name">{social.name}</h3>
                <p className="social-description">{social.description}</p>
              </a>
            ))}
          </div>
        </div>
      </section>

      {/* Visiting Tips Section */}
      <section className="visiting-tips section">
        <div className="container">
          <h2 className="section-title">Visiting Tips</h2>
          <p className="section-subtitle">
            Make the most of your visit to Mubwiza Garden
          </p>
          
          <div className="tips-grid">
            {visitingTips.map((tip, index) => (
              <div 
                key={tip.title}
                className={`tip-card ${isVisible ? 'fade-in-up' : ''}`}
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="tip-icon">{tip.icon}</div>
                <h3 className="tip-title">{tip.title}</h3>
                <p className="tip-description">{tip.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="contact-cta section">
        <div className="container">
          <div className={`cta-content ${isVisible ? 'fade-in-up' : ''}`}>
            <h2 className="cta-title">Ready to Visit?</h2>
            <p className="cta-description">
              We can't wait to share the beauty of Mubwiza Garden with you. 
              Contact us to plan your visit or learn more about our garden.
            </p>
            <div className="cta-buttons">
              <a href="tel:+250XXXXXXX" className="btn btn-primary">Call Us Now</a>
              <a href="/" className="btn btn-secondary">Explore Our Garden</a>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Contact;
