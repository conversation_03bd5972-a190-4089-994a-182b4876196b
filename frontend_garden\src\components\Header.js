import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';

const Header = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const location = useLocation();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const categories = [
    { name: 'Flowers', path: '/category/flowers' },
    { name: 'Vegetables', path: '/category/vegetables' },
    { name: 'Fruits', path: '/category/fruits' },
    { name: 'Tea & Spices', path: '/category/tea-spices' },
    { name: 'Seedlings', path: '/category/seedlings' }
  ];

  return (
    <header className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 border-b border-garden-primary/10 ${
      isScrolled
        ? 'bg-white/98 backdrop-blur-md shadow-lg'
        : 'bg-white/95 backdrop-blur-sm'
    }`}>
      <div className="max-w-7xl mx-auto px-6">
        <div className={`flex items-center justify-between transition-all duration-300 ${
          isScrolled ? 'py-3' : 'py-4'
        }`}>
          <Link
            to="/"
            className="flex items-center font-dancing text-3xl font-semibold text-garden-primary hover:scale-105 transition-transform duration-300"
          >
            <div className="w-12 h-12 bg-gradient-to-br from-garden-primary to-garden-secondary rounded-full flex items-center justify-center mr-3 animate-bounce-slow shadow-lg">
              <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2M12 8C10.9 8 10 8.9 10 10S10.9 12 12 12 14 11.1 14 10 13.1 8 12 8M12 14C9.79 14 8 15.79 8 18S9.79 22 12 22 16 20.21 16 18 14.21 14 12 14Z"/>
              </svg>
            </div>
            <span className="bg-gradient-to-r from-garden-accent to-yellow-400 bg-clip-text text-transparent">
              Mubwiza Garden
            </span>
          </Link>

          <nav className={`hidden md:flex items-center space-x-8 ${
            isMobileMenuOpen ? 'block' : 'hidden'
          } md:block`}>
            <Link
              to="/"
              className={`relative font-medium transition-colors duration-300 group ${
                location.pathname === '/'
                  ? 'text-garden-primary'
                  : 'text-gray-700 hover:text-garden-primary'
              }`}
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Home
              <span className={`absolute bottom-0 left-0 h-0.5 bg-garden-primary transition-all duration-300 ${
                location.pathname === '/' ? 'w-full' : 'w-0 group-hover:w-full'
              }`}></span>
            </Link>

            <div className="relative group">
              <span className="text-gray-700 hover:text-garden-primary font-medium cursor-pointer transition-colors duration-300 flex items-center">
                Categories
                <svg className="w-4 h-4 ml-1 transform group-hover:rotate-180 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </span>
              <div className="absolute top-full left-0 mt-2 w-48 bg-white rounded-xl shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 transform translate-y-2 group-hover:translate-y-0 z-50">
                <div className="py-2">
                  {categories.map((category) => (
                    <Link
                      key={category.name}
                      to={category.path}
                      className="block px-4 py-3 text-gray-700 hover:text-garden-primary hover:bg-garden-soft transition-all duration-200 border-l-3 border-transparent hover:border-garden-primary"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      {category.name}
                    </Link>
                  ))}
                </div>
              </div>
            </div>

            <Link
              to="/about"
              className={`relative font-medium transition-colors duration-300 group ${
                location.pathname === '/about'
                  ? 'text-garden-primary'
                  : 'text-gray-700 hover:text-garden-primary'
              }`}
              onClick={() => setIsMobileMenuOpen(false)}
            >
              About
              <span className={`absolute bottom-0 left-0 h-0.5 bg-garden-primary transition-all duration-300 ${
                location.pathname === '/about' ? 'w-full' : 'w-0 group-hover:w-full'
              }`}></span>
            </Link>

            <Link
              to="/contact"
              className={`relative font-medium transition-colors duration-300 group ${
                location.pathname === '/contact'
                  ? 'text-garden-primary'
                  : 'text-gray-700 hover:text-garden-primary'
              }`}
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Contact
              <span className={`absolute bottom-0 left-0 h-0.5 bg-garden-primary transition-all duration-300 ${
                location.pathname === '/contact' ? 'w-full' : 'w-0 group-hover:w-full'
              }`}></span>
            </Link>
          </nav>

          <button
            className="md:hidden flex flex-col justify-center items-center w-8 h-8 space-y-1.5 text-garden-primary"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          >
            <span className={`w-6 h-0.5 bg-current transform transition-all duration-300 ${
              isMobileMenuOpen ? 'rotate-45 translate-y-2' : ''
            }`}></span>
            <span className={`w-6 h-0.5 bg-current transition-all duration-300 ${
              isMobileMenuOpen ? 'opacity-0' : ''
            }`}></span>
            <span className={`w-6 h-0.5 bg-current transform transition-all duration-300 ${
              isMobileMenuOpen ? '-rotate-45 -translate-y-2' : ''
            }`}></span>
          </button>
        </div>

        {/* Mobile Menu */}
        <div className={`md:hidden transition-all duration-300 overflow-hidden ${
          isMobileMenuOpen ? 'max-h-96 pb-6' : 'max-h-0'
        }`}>
          <nav className="flex flex-col space-y-4">
            <Link
              to="/"
              className={`py-2 font-medium transition-colors duration-300 ${
                location.pathname === '/'
                  ? 'text-garden-primary'
                  : 'text-gray-700'
              }`}
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Home
            </Link>

            <div className="py-2">
              <span className="text-gray-700 font-medium">Categories</span>
              <div className="ml-4 mt-2 space-y-2">
                {categories.map((category) => (
                  <Link
                    key={category.name}
                    to={category.path}
                    className="block py-1 text-gray-600 hover:text-garden-primary transition-colors duration-200"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    {category.name}
                  </Link>
                ))}
              </div>
            </div>

            <Link
              to="/about"
              className={`py-2 font-medium transition-colors duration-300 ${
                location.pathname === '/about'
                  ? 'text-garden-primary'
                  : 'text-gray-700'
              }`}
              onClick={() => setIsMobileMenuOpen(false)}
            >
              About
            </Link>

            <Link
              to="/contact"
              className={`py-2 font-medium transition-colors duration-300 ${
                location.pathname === '/contact'
                  ? 'text-garden-primary'
                  : 'text-gray-700'
              }`}
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Contact
            </Link>
          </nav>
        </div>
      </div>
    </header>
  );
};

export default Header;
