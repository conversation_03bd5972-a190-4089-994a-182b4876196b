@echo off
echo 🌱 Mubwiza Garden - Deployment Script
echo =====================================
echo.

echo 📁 Creating build folder...
if not exist "build" mkdir build

echo 📄 Copying main website file...
copy "public\enhanced-demo.html" "build\index.html" >nul

echo 🎨 Copying favicon files...
copy "public\favicon.svg" "build\favicon.svg" >nul
copy "public\favicon.ico" "build\favicon.ico" >nul
copy "public\apple-touch-icon.png" "build\apple-touch-icon.png" >nul

echo 🖼️ Copying images...
xcopy "..\images" "build\images" /E /I /Y >nul

echo ✅ Build complete!
echo.
echo 🚀 Your website is ready for deployment!
echo 📁 Build folder: %cd%\build
echo.
echo 🌐 Next steps:
echo 1. Go to https://netlify.com
echo 2. Drag the 'build' folder to deploy
echo 3. Your site will be live instantly!
echo.
echo 🌿 Growing beauty, naturally 🌿
pause
