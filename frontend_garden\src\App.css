.App {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

main {
  flex: 1;
}

/* Color scheme based on garden theme */
:root {
  --primary-green: #2d5016;
  --secondary-green: #4a7c59;
  --light-green: #8fbc8f;
  --accent-gold: #daa520;
  --warm-brown: #8b4513;
  --cream: #f5f5dc;
  --soft-white: #fafafa;
  --text-dark: #2c3e50;
  --text-light: #7f8c8d;
  --shadow: rgba(0, 0, 0, 0.1);
  --shadow-hover: rgba(0, 0, 0, 0.2);
}

/* Container styles */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.section {
  padding: 80px 0;
}

.section-title {
  font-family: 'Dancing Script', cursive;
  font-size: 3rem;
  font-weight: 600;
  color: var(--primary-green);
  text-align: center;
  margin-bottom: 20px;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, var(--accent-gold), var(--secondary-green));
  border-radius: 2px;
}

.section-subtitle {
  font-size: 1.2rem;
  color: var(--text-light);
  text-align: center;
  margin-bottom: 60px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* Button styles */
.btn {
  display: inline-block;
  padding: 12px 30px;
  border: none;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 500;
  text-decoration: none;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-green), var(--secondary-green));
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(45, 80, 22, 0.3);
}

.btn-secondary {
  background: transparent;
  color: var(--primary-green);
  border: 2px solid var(--primary-green);
}

.btn-secondary:hover {
  background: var(--primary-green);
  color: white;
  transform: translateY(-2px);
}

/* Card styles */
.card {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 5px 15px var(--shadow);
  transition: all 0.3s ease;
  position: relative;
}

.card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px var(--shadow-hover);
}

.card-image {
  width: 100%;
  height: 250px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.card:hover .card-image {
  transform: scale(1.1);
}

.card-content {
  padding: 25px;
}

.card-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--primary-green);
  margin-bottom: 10px;
}

.card-description {
  color: var(--text-light);
  line-height: 1.6;
  margin-bottom: 20px;
}

/* Grid layouts */
.grid {
  display: grid;
  gap: 30px;
}

.grid-2 {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-3 {
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

.grid-4 {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

/* Responsive design */
@media (max-width: 768px) {
  .section {
    padding: 60px 0;
  }
  
  .section-title {
    font-size: 2.5rem;
  }
  
  .container {
    padding: 0 15px;
  }
  
  .grid {
    gap: 20px;
  }
}
