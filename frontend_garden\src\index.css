@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Dancing+Script:wght@400;500;600;700&display=swap');

@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    font-family: 'Poppins', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
  }
}

@layer components {
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    @apply w-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-garden-primary rounded;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-garden-primary/80;
  }

  /* Custom component classes */
  .btn-garden {
    @apply inline-block px-8 py-3 rounded-full font-medium text-center cursor-pointer transition-all duration-300 ease-in-out;
  }

  .btn-garden-primary {
    @apply btn-garden bg-garden-gradient text-white hover:-translate-y-1 hover:shadow-lg;
  }

  .btn-garden-secondary {
    @apply btn-garden bg-transparent border-2 border-garden-primary text-garden-primary hover:bg-garden-primary hover:text-white;
  }

  .card-garden {
    @apply bg-white rounded-2xl overflow-hidden shadow-garden transition-all duration-300 ease-in-out hover:-translate-y-3 hover:shadow-garden-hover;
  }

  .section-title {
    @apply font-dancing text-5xl font-semibold text-garden-primary text-center mb-5 relative;
  }

  .section-title::after {
    content: '';
    @apply absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-20 h-1 bg-accent-gradient rounded;
  }

  .section-subtitle {
    @apply text-xl text-text-light text-center mb-16 max-w-2xl mx-auto;
  }

  .hero-title {
    @apply font-dancing text-6xl font-bold mb-5 drop-shadow-lg leading-tight;
  }

  .category-icon {
    @apply text-6xl mb-5 animate-bounce-slow;
  }

  .hover-scale {
    @apply transition-transform duration-300 ease-in-out hover:scale-105;
  }

  .hover-lift {
    @apply transition-all duration-300 ease-in-out hover:-translate-y-2 hover:shadow-garden-lg;
  }

  .fade-in-up {
    @apply animate-fade-in-up;
  }

  .slide-in-left {
    @apply animate-slide-in-left;
  }

  .slide-in-right {
    @apply animate-slide-in-right;
  }
}
