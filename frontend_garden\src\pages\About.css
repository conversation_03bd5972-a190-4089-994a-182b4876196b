/* About Hero Section */
.about-hero {
  position: relative;
  height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  margin-top: 80px;
}

.about-hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

.about-hero-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  animation: slowZoom 20s ease-in-out infinite alternate;
}

.about-hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(45, 80, 22, 0.8),
    rgba(74, 124, 89, 0.7)
  );
}

.about-hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  color: white;
  padding: 0 20px;
}

.about-hero-title {
  font-family: 'Dancing Script', cursive;
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 20px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.about-hero-subtitle {
  font-size: 1.3rem;
  font-weight: 300;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

/* Story Section */
.about-story {
  background: white;
}

.story-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.story-paragraphs p {
  font-size: 1.1rem;
  line-height: 1.8;
  color: var(--text-dark);
  margin-bottom: 25px;
}

.story-image {
  position: relative;
}

.story-img {
  width: 100%;
  height: 400px;
  object-fit: cover;
  border-radius: 20px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

/* Values Section */
.about-values {
  background: linear-gradient(135deg, var(--soft-white), var(--cream));
  position: relative;
}

.about-values::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('/images/mubwiza background image.png') center/cover;
  opacity: 0.03;
  z-index: 0;
}

.values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
  position: relative;
  z-index: 1;
}

.value-card {
  background: white;
  padding: 40px 30px;
  border-radius: 20px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.value-icon {
  font-size: 4rem;
  margin-bottom: 20px;
  animation: bounce 2s infinite;
}

.value-title {
  font-size: 1.4rem;
  font-weight: 600;
  color: var(--primary-green);
  margin-bottom: 15px;
}

.value-description {
  color: var(--text-light);
  line-height: 1.6;
}

/* Features Section */
.about-features {
  background: white;
}

.features-grid {
  display: flex;
  flex-direction: column;
  gap: 60px;
}

.feature-card {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 50px;
  align-items: center;
}

.feature-right {
  direction: rtl;
}

.feature-right .feature-content {
  direction: ltr;
}

.feature-image img {
  width: 100%;
  height: 300px;
  object-fit: cover;
  border-radius: 15px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.feature-title {
  font-size: 1.8rem;
  font-weight: 600;
  color: var(--primary-green);
  margin-bottom: 20px;
}

.feature-description {
  font-size: 1.1rem;
  line-height: 1.7;
  color: var(--text-dark);
}

/* Mission Section */
.about-mission {
  background: linear-gradient(135deg, var(--primary-green), var(--secondary-green));
  color: white;
  position: relative;
}

.about-mission::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('/images/mubwiza background image.png') center/cover;
  opacity: 0.1;
  z-index: 0;
}

.mission-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
  position: relative;
  z-index: 1;
}

.mission-description {
  font-size: 1.2rem;
  line-height: 1.8;
  margin-bottom: 40px;
  color: rgba(255, 255, 255, 0.95);
}

.mission-stats {
  display: flex;
  gap: 40px;
}

.stat {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--accent-gold);
  margin-bottom: 5px;
}

.stat-label {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.mission-images {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.mission-img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 15px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.mission-img:first-child {
  transform: translateY(-20px);
}

.mission-img:last-child {
  transform: translateY(20px);
}

/* CTA Section */
.about-cta {
  background: var(--cream);
  text-align: center;
}

.cta-title {
  font-family: 'Dancing Script', cursive;
  font-size: 2.5rem;
  font-weight: 600;
  color: var(--primary-green);
  margin-bottom: 20px;
}

.cta-description {
  font-size: 1.2rem;
  line-height: 1.7;
  color: var(--text-dark);
  max-width: 600px;
  margin: 0 auto 40px;
}

.cta-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .story-content,
  .mission-content {
    gap: 40px;
  }
  
  .feature-card {
    gap: 40px;
  }
  
  .mission-stats {
    gap: 30px;
  }
}

@media (max-width: 768px) {
  .about-hero {
    height: 50vh;
  }
  
  .about-hero-title {
    font-size: 2.8rem;
  }
  
  .story-content,
  .mission-content,
  .feature-card {
    grid-template-columns: 1fr;
    gap: 30px;
  }
  
  .feature-right {
    direction: ltr;
  }
  
  .values-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 25px;
  }
  
  .value-card {
    padding: 30px 20px;
  }
  
  .mission-stats {
    justify-content: center;
    gap: 20px;
  }
  
  .mission-images {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .mission-img:first-child,
  .mission-img:last-child {
    transform: none;
  }
  
  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }
}

@media (max-width: 480px) {
  .about-hero-title {
    font-size: 2.2rem;
  }
  
  .about-hero-subtitle {
    font-size: 1.1rem;
  }
  
  .story-paragraphs p {
    font-size: 1rem;
  }
  
  .value-icon {
    font-size: 3rem;
  }
  
  .feature-title {
    font-size: 1.5rem;
  }
  
  .stat-number {
    font-size: 2rem;
  }
  
  .cta-title {
    font-size: 2rem;
  }
}
