<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test - Mubwiza Garden</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .test-image {
            width: 200px;
            height: 150px;
            object-fit: cover;
            margin: 10px;
            border-radius: 8px;
            border: 2px solid #ddd;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .error {
            color: red;
            font-weight: bold;
        }
        h1 {
            color: #2d5016;
            text-align: center;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🌱 Mubwiza Garden - Image Test</h1>
        <p>This page tests if all images are loading correctly:</p>
        
        <div class="grid">
            <div>
                <h3>Background Image</h3>
                <img src="images/mubwiza background image.png" alt="Background" class="test-image">
            </div>
            
            <div>
                <h3>Flowers Category</h3>
                <img src="images/flowers categories.jpg" alt="Flowers" class="test-image">
            </div>
            
            <div>
                <h3>Vegetables Category</h3>
                <img src="images/vegetables categories.jpg" alt="Vegetables" class="test-image">
            </div>
            
            <div>
                <h3>Fruits Category</h3>
                <img src="images/fruits categories.jpg" alt="Fruits" class="test-image">
            </div>
            
            <div>
                <h3>Tea & Spices Category</h3>
                <img src="images/tea spices categories.jpg" alt="Tea & Spices" class="test-image">
            </div>
            
            <div>
                <h3>Seedlings Category</h3>
                <img src="images/seedling categories.jpg" alt="Seedlings" class="test-image">
            </div>
            
            <div>
                <h3>Red Roses</h3>
                <img src="images/red roses.jpeg" alt="Red Roses" class="test-image">
            </div>
            
            <div>
                <h3>Tomatoes</h3>
                <img src="images/tomatoes.jpeg" alt="Tomatoes" class="test-image">
            </div>
            
            <div>
                <h3>Strawberries</h3>
                <img src="images/strowberries.jpeg" alt="Strawberries" class="test-image">
            </div>
        </div>
        
        <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
            <h3>✅ If you can see all images above, your deployment is working correctly!</h3>
            <p><strong>Next steps:</strong></p>
            <ol>
                <li>Go back to <a href="index.html" style="color: #2d5016; font-weight: bold;">index.html</a> (main website)</li>
                <li>If the main site still shows blank, check browser console for errors (F12)</li>
                <li>Make sure you're accessing via HTTPS (not HTTP)</li>
            </ol>
        </div>
    </div>
</body>
</html>
